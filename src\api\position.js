import request  from './request.js';

// 根据地图ID和位置名称查询位置
export const getPositionById = info => {
    return request({
        url: '/api-j/PositionInformationResource/list',
        method: 'post',
        data: info,
    });
};

// 保存位置列表(批量)
export const savePositionList = positionList => {
    return request({
        url: '/api-j/PositionInformationResource/create',
        method: 'post',
        data: positionList,
    });
};

// 修改位置信息(单个)
export const updatePositionInfo = positionInfo => {
    return request({
        url: '/api-j/PositionInformationResource/update',
        method: 'post',
        data: positionInfo,
    });
};

// 根据地图ID和位置ID删除位置(单个)
export const deletePosition = idInfo => {
    return request({
        url: '/api-j/PositionInformationResource/delete',
        method: 'post',
        data: idInfo,
    });
};

// 根据地图ID和位置ID获取位置信息(单个)
export const getPositionInfo = idInfo => {
    return request({
        url: '/api-j/PositionInformationResource/get',
        method: 'post',
        data: idInfo,
    });
};
