/**
 * 路径交互管理器 - 处理路径的拖拽、点击等交互
 */
import * as THREE from 'three'

export class PathInteractionManager {
  constructor(scene, camera, renderer, pathSystemManager) {
    this.scene = scene
    this.camera = camera
    this.renderer = renderer
    this.pathSystemManager = pathSystemManager

    // 交互状态
    this.isDragging = false
    this.dragObject = null
    this.dragStartPosition = new THREE.Vector3()
    this.dragOffset = new THREE.Vector3()

    // 射线投射器
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()

    // 路径线条现在使用Mesh几何体，不需要Line的threshold参数
    // Mesh的射线检测会更精确，只有点击几何体本身才会被检测到

    // 地面平面（用于拖拽计算，Z=0.1避免与地板重叠）
    this.groundPlane = new THREE.Plane(new THREE.Vector3(0, 0, 1), -0.1)

    // 轨道控制器引用
    this.controls = null

    // 回调函数
    this.pathSelectCallback = null

    // 绑定事件
    this.bindEvents()
  }

  // 绑定事件
  bindEvents() {
    this.onClick = this.onClick.bind(this)
    this.onMouseDown = this.onMouseDown.bind(this)
    this.onMouseMove = this.onMouseMove.bind(this)
    this.onMouseUp = this.onMouseUp.bind(this)
    this.onDoubleClick = this.onDoubleClick.bind(this)
  }

  // 启用交互（现在通过SceneSetup统一管理，只需要设置container）
  enable(container) {
    this.container = container
    // 监听拖拽相关事件和点击事件（用于添加控制点）
    container.addEventListener('mousedown', this.onMouseDown)
    container.addEventListener('mousemove', this.onMouseMove)
    container.addEventListener('mouseup', this.onMouseUp)
    container.addEventListener('click', this.onClick)
    container.addEventListener('dblclick', this.onDoubleClick)
  }

  // 禁用交互
  disable() {
    if (this.container) {
      this.container.removeEventListener('mousedown', this.onMouseDown)
      this.container.removeEventListener('mousemove', this.onMouseMove)
      this.container.removeEventListener('mouseup', this.onMouseUp)
      this.container.removeEventListener('click', this.onClick)
      this.container.removeEventListener('dblclick', this.onDoubleClick)
    }
  }

  // 点击事件 - 处理选中路径上的控制点添加
  onClick(event) {
    // 如果正在拖拽，不处理点击事件
    if (this.isDragging) {
      return false
    }

    event.preventDefault()
    this.updateMousePosition(event)
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 检测贝塞尔曲线线条（包括碰撞检测网格）
    const pathLines = this.getAllPathLines()
    const intersects = this.raycaster.intersectObjects(pathLines)

    if (intersects.length > 0) {
      const intersectedLine = intersects[0].object
      const userData = intersectedLine.userData

      if ((userData.objectType === 'pathLine' || userData.objectType === 'pathLineCollision') && userData.clickable) {
        const path = this.pathSystemManager.getPath(userData.pathUuid)

        // 只有当路径是贝塞尔曲线且已经被选中时，才添加控制点
        if (path && path.type === 'bezier' && path.isSelected) {
          // 计算鼠标射线与地面平面的交点，这是用户实际点击的位置
          const clickPoint = new THREE.Vector3()
          this.raycaster.ray.intersectPlane(this.groundPlane, clickPoint)

          // 限制在XY平面，避免与地板重叠
          clickPoint.z = 0.1

          path.addControlPoint(clickPoint)

          // 阻止事件传播，避免被SceneSetup处理
          event.stopPropagation()
          event.stopImmediatePropagation()
          return true // 返回true表示已处理
        }
      }
    }

    // 如果没有处理，让SceneSetup处理路径选择
    return false
  }

  // 鼠标按下事件 - 处理控制点拖拽
  onMouseDown(event) {
    event.preventDefault()

    this.updateMousePosition(event)

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 检测路径控制点
    const controlPoints = this.getAllControlPoints()
    const intersects = this.raycaster.intersectObjects(controlPoints)

    if (intersects.length > 0) {
      const intersectedObject = intersects[0].object
      const userData = intersectedObject.userData

      if (userData.objectType === 'pathControlPoint') {
        // 如果控制点可拖拽，开始拖拽（这会自动进入编辑状态）
        if (userData.draggable) {
          this.startDragControlPoint(intersectedObject, intersects[0].point)
        }

        // 阻止事件传播，避免被其他处理器干扰
        event.stopPropagation()
        event.stopImmediatePropagation()
        return
      }
    }
  }

  // 鼠标移动事件
  onMouseMove(event) {
    event.preventDefault()

    this.updateMousePosition(event)

    if (this.isDragging && this.dragObject) {
      // 计算新位置
      this.raycaster.setFromCamera(this.mouse, this.camera)
      const intersectPoint = new THREE.Vector3()
      this.raycaster.ray.intersectPlane(this.groundPlane, intersectPoint)

      if (intersectPoint) {
        // 应用拖拽偏移
        const newPosition = intersectPoint.sub(this.dragOffset)

        // 更新控制点位置
        this.updateDraggedControlPoint(newPosition)
      }
    }
  }

  // 鼠标抬起事件
  onMouseUp(event) {
    event.preventDefault()

    if (this.isDragging) {
      this.stopDrag()
    }
  }

  // 双击事件 - 处理控制点删除
  onDoubleClick(event) {
    event.preventDefault()
    event.stopPropagation() // 阻止事件冒泡

    this.updateMousePosition(event)
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 首先检测贝塞尔控制点（黄色球体）
    const controlPoints = this.getAllControlPoints()
    const controlPointIntersects = this.raycaster.intersectObjects(controlPoints)

    if (controlPointIntersects.length > 0) {
      const intersectedControlPoint = controlPointIntersects[0].object
      const userData = intersectedControlPoint.userData

      // 只删除贝塞尔控制点，不删除起点和终点
      if (userData.objectType === 'pathControlPoint' && userData.pointType === 'bezierControl') {
        const path = this.pathSystemManager.getPath(userData.pathUuid)
        if (path && path.type === 'bezier' && userData.pointIndex >= 0) {
          // 删除控制点
          path.removeControlPoint(userData.pointIndex)
          console.log(`删除贝塞尔控制点，索引: ${userData.pointIndex}`)
          return // 处理完成，不再继续
        }
      }
    }

    // 如果没有双击控制点，则不执行任何操作
    // 移除了原来的双击线条添加控制点功能，现在改为单击添加
  }

  // 更新鼠标位置
  updateMousePosition(event) {
    const rect = this.container.getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  }

  // 开始拖拽控制点
  startDragControlPoint(controlPoint, intersectPoint) {
    this.isDragging = true
    this.dragObject = controlPoint

    // 计算拖拽偏移
    this.dragOffset.copy(intersectPoint).sub(controlPoint.position)

    // 禁用轨道控制器以防止场景旋转
    if (this.controls) {
      this.controls.enabled = false
    }

    // 开始编辑对应的路径
    const pathUuid = controlPoint.userData.pathUuid
    this.pathSystemManager.startEditing(pathUuid)
  }

  // 更新被拖拽的控制点
  updateDraggedControlPoint(newPosition) {
    if (!this.dragObject) return

    const userData = this.dragObject.userData
    const path = this.pathSystemManager.getPath(userData.pathUuid)

    if (path) {
      path.updateControlPoint(
        userData.pointType,
        newPosition,
        userData.pointIndex
      )
    }
  }

  // 停止拖拽
  stopDrag() {
    // 重新启用轨道控制器
    if (this.controls) {
      this.controls.enabled = true
    }

    // 拖拽结束后，保持编辑状态（黄色），这样控制点继续可见，方便用户继续调整
    // 不调用stopEditing()，让路径保持编辑状态
    // 只有当用户点击空白处或其他对象时，才会退出编辑状态

    this.isDragging = false
    this.dragObject = null
    this.dragOffset.set(0, 0, 0)
  }

  // 设置轨道控制器引用
  setControls(controls) {
    this.controls = controls
  }

  // 设置路径选中回调（保留用于拖拽时的通知）
  setPathSelectCallback(callback) {
    this.pathSelectCallback = callback
  }

  // 检查当前是否有路径被选中（包括通过端点选中的）
  hasSelectedPath() {
    return this.pathSystemManager.selectedPath !== null
  }

  // 获取所有控制点
  getAllControlPoints() {
    const controlPoints = []

    this.pathSystemManager.paths.forEach(path => {
      if (path.startControlPoint) controlPoints.push(path.startControlPoint)
      if (path.endControlPoint) controlPoints.push(path.endControlPoint)
      if (path.bezierControlMeshes) {
        controlPoints.push(...path.bezierControlMeshes)
      }
    })

    return controlPoints
  }

  // 获取所有路径线条（包括碰撞检测网格）
  getAllPathLines() {
    const pathLines = []

    this.pathSystemManager.paths.forEach(path => {
      // 优先使用碰撞检测网格进行射线检测，提供更大的点击范围
      if (path.collisionMesh) {
        pathLines.push(path.collisionMesh)
      } else if (path.line) {
        // 如果没有碰撞网格，回退到可视线条
        pathLines.push(path.line)
      }
    })

    return pathLines
  }



  // 清除选择状态的方法
  clearSelection() {
    if (this.pathSystemManager) {
      this.pathSystemManager.clearAllSelections()
    }
  }

  // 销毁
  dispose() {
    this.disable()
  }
}
