<template>
  <div class="map3d-container">
    <!-- 第一列：地图信息 -->
    <div class="map-list-panel">
      <h3>地图信息</h3>

      <!-- 如果没有选择地图，显示空状态 -->
      <a-empty v-if="!currentMapId" description="请打开地图以展示信息" :image="false">
        <template #description>
          <span style="color: #999;">请打开地图以展示信息</span>
        </template>
      </a-empty>

      <!-- 如果已选择地图，显示树形结构 -->
      <a-tree v-else v-model:expandedKeys="expandedKeys" v-model:selectedKeys="selectedKeys" :load-data="onLoadData"
        :tree-data="treeData" @select="onTreeSelect" @expand="onTreeExpand">
        <template #title="{ title, key, objectType, isLeaf }">
          <span @click="handleTitleClick(key, objectType, isLeaf, $event)"
                :style="{ cursor: isLeaf ? 'pointer' : (objectType === 'category' ? 'pointer' : 'default') }">
            {{ title }}
          </span>
        </template>
      </a-tree>
    </div>

    <!-- 第二列：3D容器 -->
    <div class="three-container-wrapper">
      <!-- 操作区 -->
      <div class="map-operations">
        <div class="map-buttons">
          <a-button type="primary" @click="showMapSelectModal">
            打开地图
          </a-button>
          <a-button type="default" @click="saveMap" :disabled="!currentMapId">
            保存地图
          </a-button>
          <a-button type="default" danger @click="deleteMap" :disabled="!currentMapId">
            删除地图
          </a-button>
          <a-button type="default" @click="showFileManageModal" :disabled="!currentMapId">
            文件管理
          </a-button>
          <a-button :type="isPCDRendered ? 'default' : 'primary'" @click="togglePCD"
            :disabled="!currentMapId || !currentMapData || !currentMapData.pcdName">
            {{ isPCDRendered ? '清除PCD' : '渲染PCD' }}
          </a-button>
          <a-button type="default" @click="togglePerformancePanel" :disabled="!renderer">
            性能监控
          </a-button>
          <a-button type="default" @click="optimizePerformance" :disabled="!renderer">
            性能优化
          </a-button>
        </div>

        <!-- 拖动元素容器 -->
        <div class="drag-items-container">
          <!-- 拖动位置元素 -->
          <div class="drag-position-item" draggable="true" @dragstart="startDrag('person', $event)">
            <span>位置</span>
          </div>

          <!-- 拖动直线路径元素 -->
          <div class="drag-straight-path-item" draggable="true" @dragstart="startDrag('straightPath', $event)">
            <span>直线</span>
          </div>

          <!-- 拖动贝塞尔曲线路径元素 -->
          <div class="drag-bezier-path-item" draggable="true" @dragstart="startDrag('bezierPath', $event)">
            <span>曲线</span>
          </div>
        </div>
      </div>

      <!-- 3D容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>

    <!-- 地图选择模态框 -->
    <a-modal v-model:visible="mapSelectModalVisible" title="选择地图" :maskClosable="false" @ok="handleMapSelect"
      @cancel="handleMapSelectCancel" okText="打开" cancelText="取消">
      <a-select v-model:value="selectedMapId" placeholder="请选择地图" style="width: 100%;">
        <a-select-option v-for="map in mapList" :key="map.id" :value="map.id">
          {{ map.name }}
        </a-select-option>
      </a-select>
    </a-modal>

    <!-- 保存地图模态框 -->
    <a-modal v-model:visible="saveMapModalVisible" title="保存地图" :maskClosable="false" @ok="handleSaveMapConfirm"
      @cancel="handleSaveMapCancel" okText="保存" cancelText="取消">
      <div class="save-map-form">
        <a-input id="mapName" v-model:value="editableMapName" placeholder="请输入地图名称"
          style="width: 100%; margin-top: 8px;" />
      </div>
    </a-modal>

    <!-- 文件管理模态框组件 -->
    <FileManageModal v-model:visible="fileManageModalVisible" :current-map-id="currentMapId"
      :current-map-data="currentMapData" @files-uploaded="handleFilesUploaded" />

    <!-- 第三列：属性编辑 -->
    <div class="control-panel">
      <!-- 性能监控面板组件 -->
      <PerformancePanel v-model:visible="showPerformancePanel" :renderer="renderer" :pcd-renderer="pcdRenderer"
        :pgm-renderer="pgmRenderer" :current-map-id="currentMapId" :current-map-data="currentMapData"
        :performance-stats="performanceStats" @optimize-performance="optimizePerformance" />

      <!-- 未选中时的空状态 -->
      <a-empty v-if="!selectedPositionData && !selectedPathData" class="empty-state" description="请选择元素以编辑属性"
        :image="false">
        <template #description>
          <span style="color: #999;">请选择位置或路径元素以编辑属性</span>
        </template>
      </a-empty>

      <!-- 选中位置时的属性编辑 -->
      <div v-if="selectedPositionData" class="object-properties">

        <h3>属性编辑</h3>

        <!-- <div class="property-group inline">
          <label>位置ID:</label>
          <a-input v-model:value="selectedPositionData.uuid" disabled size="small" />
        </div> -->
        <div class="property-group inline">
          <label>位置名称:</label>
          <a-input v-model:value="selectedPositionData.name"
            @change="e => updatePositionProperty('name', e.target.value)" size="small" />
        </div>
        <div class="property-group inline">
          <label>X坐标:</label>
          <a-input-number v-model:value="selectedPositionData.xcoordinate" :min="coordinateBounds.minX"
            :max="coordinateBounds.maxX" :step="0.01" :precision="2"
            @change="val => updatePositionProperty('xcoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>Y坐标(深度):</label>
          <a-input-number v-model:value="selectedPositionData.ycoordinate" :min="coordinateBounds.minY"
            :max="coordinateBounds.maxY" :step="0.01" :precision="2"
            @change="val => updatePositionProperty('ycoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>Z坐标(高度):</label>
          <a-input-number v-model:value="selectedPositionData.zcoordinate" :min="coordinateBounds.minZ"
            :max="coordinateBounds.maxZ" :step="0.01" :precision="2"
            @change="val => updatePositionProperty('zcoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <a-tooltip placement="top">
            <template #title>
              <div>
                0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左<br>
                交互方式：Shift+滚轮 | 拖拽圆盘 | Q/E | Ctrl+左右箭头
              </div>
            </template>
            <label style="cursor: help;">Yaw(°):</label>
          </a-tooltip>
          <a-input-number :min="0" :max="360" :step="15" :precision="2"
            :value="Number(selectedPositionData.yaw || 0)"
            @change="val => updatePositionProperty('yaw', String(val))" size="small" />
        </div>
        <div class="property-group inline">
          <label>动作编号:</label>
          <a-input v-model:value="selectedPositionData.actionNo"
            @change="e => updatePositionProperty('actionNo', e.target.value)" size="small" />
        </div>
        <div class="property-group block">
          <label>内容详情:</label>
          <a-tabs v-model:activeKey="contentDetailActiveTab" size="small">
            <a-tab-pane key="zh" tab="中文">
              <a-textarea v-model:value="selectedPositionData.contentDetailZh"
                @change="e => updatePositionProperty('contentDetailZh', e.target.value)" :rows="10" size="small"
                placeholder="请输入中文讲解内容" />
            </a-tab-pane>
            <a-tab-pane key="en" tab="English">
              <a-textarea v-model:value="selectedPositionData.contentDetailEn"
                @change="e => updatePositionProperty('contentDetailEn', e.target.value)" :rows="10" size="small"
                placeholder="Please enter English content" />
            </a-tab-pane>
            <a-tab-pane key="ja" tab="日本語">
              <a-textarea v-model:value="selectedPositionData.contentDetailJa"
                @change="e => updatePositionProperty('contentDetailJa', e.target.value)" :rows="10" size="small"
                placeholder="日本語の内容を入力してください" />
            </a-tab-pane>
            <a-tab-pane key="ko" tab="한국어">
              <a-textarea v-model:value="selectedPositionData.contentDetailKo"
                @change="e => updatePositionProperty('contentDetailKo', e.target.value)" :rows="10" size="small"
                placeholder="한국어 내용을 입력하세요" />
            </a-tab-pane>
          </a-tabs>
        </div>
        <!-- 操作按钮 -->
        <div class="position-actions">
          <a-button block danger @click="deleteSelectedPosition">
            删除位置
          </a-button>
        </div>
      </div>

      <!-- 选中路径时的属性编辑 -->
      <div v-if="selectedPathData" class="object-properties">
        <h3>路径属性编辑</h3>

        <div class="property-group inline">
          <label>名称:</label>
          <a-input v-model:value="selectedPathData.name" @change="e => updatePathProperty('name', e.target.value)"
            size="small" />
        </div>
        <div class="property-group inline">
          <label>类型:</label>
          <a-input :value="selectedPathData.type === 'straight' ? '直线' : '贝塞尔曲线'" disabled size="small" />
        </div>
        <div class="property-group inline">
          <label>位置A:</label>
          <a-select v-model:value="selectedPathData.positionA" @change="handlePositionAChange" size="small"
            placeholder="选择位置A">
            <a-select-option v-for="position in currentMapPositions" :key="position.uuid" :value="position.uuid">
              {{ position.name }}
            </a-select-option>
          </a-select>
        </div>
        <div class="property-group inline">
          <label>位置B:</label>
          <a-select v-model:value="selectedPathData.positionB" @change="handlePositionBChange" size="small"
            placeholder="选择位置B">
            <a-select-option v-for="position in currentMapPositions" :key="position.uuid" :value="position.uuid">
              {{ position.name }}
            </a-select-option>
          </a-select>
        </div>
        <div class="property-group inline">
          <label>方向:</label>
          <a-select :value="selectedPathData ? selectedPathData.direction : 'A_to_B'" @change="handleDirectionChange"
            size="small">
            <a-select-option value="A_to_B">A->B</a-select-option>
            <a-select-option value="B_to_A">B->A</a-select-option>
            <a-select-option value="bidirectional">A&lt;->B</a-select-option>
          </a-select>
        </div>

        <div class="property-group inline">
          <label>线速度(m/s):</label>
          <a-input-number v-model:value="selectedPathData.linearVelocity" :min="0" :max="10" :step="0.1" :precision="2"
            @change="val => updatePathProperty('linearVelocity', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>角速度(rad/s):</label>
          <a-input-number v-model:value="selectedPathData.angularVelocity" :min="0" :max="5" :step="0.1" :precision="2"
            @change="val => updatePathProperty('angularVelocity', val)" size="small" />
        </div>
        <div class="property-group block">
          <label>路径描述:</label>
          <a-textarea v-model:value="selectedPathData.description"
            @change="e => updatePathProperty('description', e.target.value)" :rows="10" size="small" />
        </div>

        <!-- 操作按钮 -->
        <div class="path-actions">
          <a-button block danger @click="deleteSelectedPath">
            删除路径
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { Person } from '../classes/Person.js'
import { DragManager } from '../managers/DragManager.js'
import { markRaw } from 'vue'

import FileManageModal from './FileManageModal.vue'
import PerformancePanel from './PerformancePanel.vue'
import { makeThreeObjectNonReactive } from '../utils/threeUtils.js'
import { message, Modal } from 'ant-design-vue'

// 导入配置和工具模块
import { MapConfig } from '../config/mapConfig.js'
import { PGMRenderer } from '../utils/pgmRenderer.js'
import { PCDRenderer } from '../utils/pcdRenderer.js'
import { TreeDataManager } from '../utils/treeDataManager.js'
import { PositionManager } from '../utils/positionManager.js'
import { SceneSetup } from '../utils/sceneSetup.js'
import { performanceMonitor } from '../utils/performanceMonitor.js'
import { renderOptimizer } from '../utils/renderOptimizer.js'
import { interactionOptimizer } from '../utils/interactionOptimizer.js'

import { deletePosition } from '../api/position.js'
import { deletePath } from '../api/path.js'
import { requestSuccess, requestFailed, showLoading, hideLoading, generateUUID } from '../utils/common.js'
import { getMapList, getMapInfo, downloadMapFile, saveMapInfo, deleteMapById } from '../api/map.js'

// 导入新的路径系统
import { PathSystemManager } from '../utils/pathSystem.js'
import { StraightPath } from '../utils/straightPath.js'
import { BezierPath } from '../utils/bezierPath.js'
import { PathInteractionManager } from '../utils/pathInteractionManager.js'

export default {
  name: 'Map3D',
  components: {
    FileManageModal,
    PerformancePanel
  },
  data() {
    return {
      scene: null, // 场景
      camera: null, // 相机
      renderer: null, // 渲染器
      controls: null, // 控制器
      dragManager: null, // 拖拽管理器
      sceneObjects: [], // 场景对象
      selectedObject: null, // 选中的对象
      selectedPositionData: null, // 选中的位置点数据（统一属性面板）
      selectedPathData: null, // 选中的路径数据（统一属性面板）
      // 当前选中的地图ID（用于拖拽时确定插入位置）
      currentSelectedMapId: null,
      // 旋转相关
      hintTimeout: null,
      // 键盘状态跟踪
      isShiftPressed: false,
      // 树形数据相关
      treeData: [],
      expandedKeys: [],
      selectedKeys: [],
      // 地图数据
      mapList: [],
      currentMapId: null,
      currentMapData: null, // 当前地图的详细数据
      // 地图选择模态框相关
      mapSelectModalVisible: false,
      selectedMapId: null,
      // 保存地图模态框相关
      saveMapModalVisible: false,
      editableMapName: '',
      // 文件管理模态框相关
      fileManageModalVisible: false,
      // 存储上传后的新文件名
      uploadedFileNames: {
        pgm: null,
        pcd: null,
        yaml: null
      },
      // PCD渲染状态
      isPCDRendered: false,
      // 工具类实例
      pgmRenderer: null,
      pcdRenderer: null,
      treeDataManager: null,
      positionManager: null,

      // 新的路径系统
      pathSystemManager: null,
      pathInteractionManager: null,

      // 性能监控相关
      performanceStats: {
        fps: 0,
        memoryMB: 0,
        points: 0
      },
      showPerformancePanel: false,

      // 内容详情标签页
      contentDetailActiveTab: 'zh'
    }
  },

  mounted() {
    // 初始化工具类
    this.initializeManagers()

    // 初始化Three.js场景
    this.initThreeJS()
    this.setupDragAndDrop()
    this.animate()

    // 初始化性能监控
    this.initPerformanceMonitoring()

    // 添加键盘事件监听
    document.addEventListener('keydown', this.onKeyDown)
    document.addEventListener('keydown', this.onKeyStateChange)
    document.addEventListener('keyup', this.onKeyStateChange)

    // 获取地图列表
    this.loadMapList();

  },
  beforeUnmount() {
    // 停止性能监控
    performanceMonitor.stop()

    if (this.renderer) {
      this.renderer.dispose()
    }

    // 清理渲染器资源
    if (this.pgmRenderer) {
      this.pgmRenderer.dispose()
    }
    if (this.pcdRenderer) {
      this.pcdRenderer.dispose()
    }

    // 清理路径交互管理器
    if (this.pathInteractionManager) {
      this.pathInteractionManager.dispose()
    }

    // 清理事件监听
    document.removeEventListener('keydown', this.onKeyDown)
    document.removeEventListener('keydown', this.onKeyStateChange)
    document.removeEventListener('keyup', this.onKeyStateChange)
  },
  computed: {
    // 计算当前的坐标边界限制（Z轴向上，Y轴水平）
    coordinateBounds() {
      // 优先使用PGM边界
      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        const bounds = this.pgmRenderer.getPGMBounds()
        if (bounds) {
          return {
            minX: Math.round(bounds.minX * 100) / 100, // 保留2位小数
            maxX: Math.round(bounds.maxX * 100) / 100,
            minY: Math.round(bounds.minY * 100) / 100, // Y轴水平
            maxY: Math.round(bounds.maxY * 100) / 100, // Y轴水平
            minZ: Math.round(bounds.minZ * 100) / 100, // Z轴向上
            maxZ: Math.round(bounds.maxZ * 100) / 100  // Z轴向上
          }
        }
      }

      // 其次使用PCD边界
      if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        const bounds = this.pcdRenderer.getPCDBounds()
        if (bounds) {
          return {
            minX: Math.round(bounds.minX * 100) / 100,
            maxX: Math.round(bounds.maxX * 100) / 100,
            minZ: Math.round(bounds.minZ * 100) / 100,
            maxZ: Math.round(bounds.maxZ * 100) / 100,
            minY: Math.round(bounds.minY * 100) / 100,
            maxY: Math.round(bounds.maxY * 100) / 100
          }
        }
      }

      // 默认边界
      return {
        minX: -10,
        maxX: 10,
        minZ: -10,
        maxZ: 10,
        minY: 0,
        maxY: 10
      }
    },

    // 获取当前地图的所有位置信息
    currentMapPositions() {
      if (!this.currentMapId || !this.treeData.length) {
        return []
      }

      const mapNode = this.treeData.find(node =>
        node.key === this.currentMapId.toString() || node.key === this.currentMapId
      )

      if (!mapNode || !mapNode.children) {
        return []
      }

      // 查找位置分类节点
      const positionsNode = mapNode.children.find(child =>
        child.categoryType === 'positions' && child.objectType === 'category'
      )

      if (!positionsNode || !positionsNode.children) {
        return []
      }

      // 返回位置数据
      return positionsNode.children
        .filter(child => child.objectType === 'position' && child.objectData)
        .map(child => ({
          uuid: child.objectData.uuid,
          name: child.objectData.name,
          xcoordinate: child.objectData.xcoordinate,
          ycoordinate: child.objectData.ycoordinate,
          zcoordinate: child.objectData.zcoordinate
        }))
    }
  },
  methods: {
    // 生成UUID
    generateUUID() {
      return generateUUID()
    },

    // 初始化管理器
    initializeManagers() {
      // 初始化工具类
      this.treeDataManager = new TreeDataManager()
      this.positionManager = new PositionManager()

      // 初始化新的路径系统
      this.pathSystemManager = new PathSystemManager()

      // 初始化空的地图数据，等待接口返回
      this.mapList = []
      this.treeData = []

      // 同步树形数据状态
      this.expandedKeys = []
      this.selectedKeys = []
    },

    // 加载地图列表
    async loadMapList() {
      try {
        const res = await getMapList()
        if (res.errorCode === 0) {
          this.mapList = res.datas || []
        } else {
          requestSuccess(res)
        }
      } catch (err) {
        requestFailed(err)
      }
    },
    // 显示地图选择模态框
    showMapSelectModal() {
      this.mapSelectModalVisible = true
    },

    // 处理地图选择确认
    async handleMapSelect() {
      const _self = this
      if (_self.selectedMapId) {
        try {
          // 调用 getMapInfo 接口获取地图详细信息
          const res = await getMapInfo(_self.selectedMapId)
          if (res.errorCode === 0) {
            // 处理地图信息和位置信息
            _self.mapSelectModalVisible = false
            _self.uploadedFileNames = {
              pgm: null,
              pcd: null,
              yaml: null
            }
            await _self.processMapInfo(res.data)
          } else {
            requestSuccess(res)
          }
        } catch (err) {
          requestFailed(err)
        }
      }
    },

    // 处理地图选择取消
    handleMapSelectCancel() {
      this.mapSelectModalVisible = false
    },

    // 处理地图信息，构建树形结构
    async processMapInfo(mapData) {
      if (!mapData) return

      console.log(`开始处理地图信息: ${mapData.name}`)

      // 清空当前场景（包括PCD和PGM）
      this.clearSceneWithoutConfirm()

      // 设置当前地图ID和数据
      this.currentMapId = mapData.id
      this.currentMapData = mapData

      // 构建树形结构数据（三级结构）
      const treeNode = {
        title: mapData.name,
        key: mapData.id.toString(),
        isLeaf: false,
        children: []
      }

      // 创建位置分类节点
      const positionsNode = {
        title: '位置',
        key: `${mapData.id}_positions`,
        isLeaf: false,
        mapId: mapData.id,
        objectType: 'category',
        categoryType: 'positions',
        children: []
      }

      // 创建路径分类节点
      const pathsNode = {
        title: '路径',
        key: `${mapData.id}_paths`,
        isLeaf: false,
        mapId: mapData.id,
        objectType: 'category',
        categoryType: 'paths',
        children: []
      }

      // 处理位置信息
      if (mapData.positionInformations && mapData.positionInformations.length > 0) {
        // 将位置信息转换为子节点，并为每个位置创建UUID
        const enhancedPositions = mapData.positionInformations.map(position => {
          // 如果已有uuid则使用，否则生成新的uuid
          const uuid = position.uuid || this.generateUUID()
          console.log(`处理位置数据 ${position.name}: yaw=${position.yaw}`)
          return {
            ...position,
            uuid: uuid
          }
        })

        enhancedPositions.forEach(position => {
          const positionNode = {
            title: position.name,
            key: `${mapData.id}_position_${position.uuid}`,
            isLeaf: true,
            mapId: mapData.id,
            objectId: position.uuid,
            objectType: 'position',  // 添加objectType属性
            objectData: {
              name: position.name,
              xcoordinate: position.xcoordinate || '0',
              ycoordinate: position.ycoordinate || '0',
              zcoordinate: position.zcoordinate || '0',
              yaw: position.yaw ? (Math.round(parseFloat(position.yaw) * 100) / 100).toString() : '0',
              actionNo: position.actionNo || '',
              contentDetailZh: position.contentDetailZh || '', // 中文内容
              contentDetailEn: position.contentDetailEn || '', // 英文内容
              contentDetailJa: position.contentDetailJa || '', // 日文内容
              contentDetailKo: position.contentDetailKo || '', // 韩文内容
              mapId: position.mapId || mapData.id.toString(),
              uuid: position.uuid,
              isSaved: true // 从服务器加载的数据标记为已保存
            }
          }
          positionsNode.children.push(positionNode)
        })

        // 将位置信息转换为3D场景对象，传递uuid
        const sceneObjects = this.convertPositionDataToSceneObjects(enhancedPositions)
        this.importObjects(sceneObjects)
      }

      // 处理路径信息
      if (mapData.pathInformations && mapData.pathInformations.length > 0) {
        console.log(`开始处理路径信息，数量: ${mapData.pathInformations.length}`)

        // 为每个路径信息创建路径对象并添加到场景和树形结构
        for (const pathInfo of mapData.pathInformations) {
          await this.createPathFromServerData(pathInfo, pathsNode, mapData.positionInformations)
        }
      }

      // 将位置和路径分类节点添加到地图节点
      treeNode.children.push(positionsNode)
      treeNode.children.push(pathsNode)

      // 更新树形数据
      this.treeData = [treeNode]
      // 自动展开地图节点和分类节点
      this.expandedKeys = [
        mapData.id.toString(),
        `${mapData.id}_positions`,
        `${mapData.id}_paths`
      ]
      this.selectedKeys = []

      // 同步到 TreeDataManager
      this.treeDataManager.setTreeData(this.treeData)
      this.treeDataManager.setExpandedKeys(this.expandedKeys)
      this.treeDataManager.setSelectedKeys(this.selectedKeys)

      // 更新位置计数器，确保新建位置不会与已有位置重名
      this.updatePositionCounterFromExistingPositions(mapData.id, mapData.positionInformations || [])

      // 如果有PGM文件，下载并渲染
      if (mapData.pgmName) {
        await this.downloadAndRenderPGM(mapData.id, mapData.pgmName, mapData.yamlName)
      }
    },

    // 从服务器数据创建路径对象
    async createPathFromServerData(pathInfo, pathsNode, originalPositions) {
      try {
        console.log(`创建路径: ${pathInfo.name}, 类型: ${pathInfo.type}`)

        // 根据positionA和positionB获取起点和终点坐标
        const startPoint = this.getPositionCoordinatesByUuid(pathInfo.positionA, originalPositions)
        const endPoint = this.getPositionCoordinatesByUuid(pathInfo.positionB, originalPositions)

        if (!startPoint || !endPoint) {
          console.warn(`路径 ${pathInfo.name} 的起点或终点位置不存在，跳过创建`)
          return
        }

        // 创建路径对象
        let pathObject
        if (pathInfo.type === 'straight') {
          pathObject = new StraightPath(pathInfo.name, pathInfo.mapId, startPoint, endPoint)
        } else if (pathInfo.type === 'bezier') {
          pathObject = new BezierPath(pathInfo.name, pathInfo.mapId, startPoint, endPoint)

          // 如果有中间控制点数据，设置控制点
          if (pathInfo.centralPoint && Array.isArray(pathInfo.centralPoint) && pathInfo.centralPoint.length > 0) {
            this.setCentralPointsToPath(pathObject, pathInfo.centralPoint, pathInfo.direction)
          }
        } else {
          console.warn(`未知的路径类型: ${pathInfo.type}`)
          return
        }

        // 设置路径属性
        pathObject.uuid = pathInfo.uuid
        pathObject.positionA = pathInfo.positionA
        pathObject.positionB = pathInfo.positionB
        pathObject.direction = pathInfo.direction || 'A_to_B'
        pathObject.linearVelocity = parseFloat(pathInfo.linearVelocity) || 1.0
        pathObject.angularVelocity = parseFloat(pathInfo.angularVelocity) || 1.0
        pathObject.description = pathInfo.description || ''
        pathObject.isSaved = true // 从服务器加载的数据标记为已保存

        // 更新group的uuid以保持一致性
        pathObject.group.uuid = pathInfo.uuid
        pathObject.group.userData.objectUuid = pathInfo.uuid

        // 添加到路径系统管理器
        this.pathSystemManager.addPath(pathObject)

        // 添加到3D场景
        this.scene.add(pathObject.group)

        // 将路径Group对象添加到sceneObjects数组，以支持点击选择
        this.sceneObjects.push(pathObject.group)

        // 添加到树形结构
        const pathNode = {
          title: pathInfo.name,
          key: `${pathInfo.mapId}_path_${pathInfo.uuid}`,
          isLeaf: true,
          mapId: pathInfo.mapId,
          objectId: pathInfo.uuid,
          objectType: 'path',
          objectData: {
            uuid: pathInfo.uuid,
            name: pathInfo.name,
            type: pathInfo.type,
            positionA: pathInfo.positionA,
            positionB: pathInfo.positionB,
            direction: pathInfo.direction,
            linearVelocity: pathInfo.linearVelocity,
            angularVelocity: pathInfo.angularVelocity,
            description: pathInfo.description,
            mapId: pathInfo.mapId,
            isSaved: true
          }
        }
        pathsNode.children.push(pathNode)

        console.log(`路径 ${pathInfo.name} 创建完成`)
      } catch (error) {
        console.error(`创建路径 ${pathInfo.name} 时出错:`, error)
      }
    },

    // 根据位置UUID获取位置坐标（支持从原始数据和树形数据中查找）
    getPositionCoordinatesByUuid(positionUuid, originalPositions = null) {
      if (!positionUuid) return null

      let position = null

      // 优先从原始位置数据中查找（用于初始化时）
      if (originalPositions && Array.isArray(originalPositions)) {
        position = originalPositions.find(pos => pos.uuid === positionUuid)
      }

      // 如果原始数据中没找到，从当前地图位置中查找
      if (!position) {
        position = this.currentMapPositions.find(pos => pos.uuid === positionUuid)
      }

      if (position) {
        return {
          x: Math.round((parseFloat(position.xcoordinate) || 0) * 100) / 100, // 保留两位小数
          y: Math.round((parseFloat(position.ycoordinate) || 0) * 100) / 100, // Y轴水平
          z: Math.round((parseFloat(position.zcoordinate) || 0) * 100) / 100  // Z轴向上
        }
      }

      console.warn(`未找到位置UUID: ${positionUuid}`)
      return null
    },

    // 为贝塞尔路径设置中间控制点
    setCentralPointsToPath(pathObject, centralPoints, direction) {
      if (!pathObject || !centralPoints || !Array.isArray(centralPoints)) {
        return
      }

      // 根据方向决定控制点顺序
      let orderedPoints = [...centralPoints]
      if (direction === 'B_to_A') {
        // B->A方向，需要反转控制点顺序（因为保存时是按A->B顺序的）
        orderedPoints = orderedPoints.reverse()
      }
      // A->B 和 A<->B 都使用正常顺序

      // 设置控制点到路径对象，保留两位小数
      pathObject.bezierControlPoints = orderedPoints.map(point => ({
        x: Math.round((parseFloat(point.x) || 0) * 100) / 100,
        y: Math.round((parseFloat(point.y) || 0) * 100) / 100, // 使用正确的Y坐标
        z: 0.1 // 固定在XY平面上，稍微抬高避免Z-fighting
      }))

      // 重新创建路径以应用控制点
      pathObject.createPath()

      console.log(`为路径 ${pathObject.name} 设置了 ${orderedPoints.length} 个中间控制点，方向: ${direction}`)
    },

    // 下载并渲染PGM文件
    async downloadAndRenderPGM(mapId, pgmName, yamlName) {

      showLoading('正在渲染PGM地图...')
      try {

        // 调用下载接口获取PGM文件
        const pgmResponse = await downloadMapFile(mapId, pgmName)

        if (pgmResponse) {
          // 接口返回的直接就是blob数据，创建URL
          const pgmUrl = URL.createObjectURL(pgmResponse)

          let yamlUrl = null

          try {
            const yamlResponse = await downloadMapFile(mapId, yamlName)
            if (yamlResponse) {
              yamlUrl = URL.createObjectURL(yamlResponse)
            }
          } catch (yamlError) {
            console.warn('YAML文件下载失败，将使用默认配置:', yamlError)
          }

          // 使用PGMRenderer渲染PGM文件
          const result = await this.pgmRenderer.renderPGM(pgmUrl, yamlUrl)

          // 隐藏默认地板和网格
          SceneSetup.hideDefaultFloor(this.scene)

          // 设置拖拽管理器的边界
          this.updateDragManagerBounds(result.bounds)

          // 调整相机位置以适应PGM地图
          this.adjustCameraForPGM(result.bounds)

          // 限制现有位置元素在PGM范围内
          this.clampExistingPositionsToPGM()

          // 更新现有位置元素的缩放比例
          this.updateExistingPersonScale()

          // 性能优化：使用交互优化器根据地图复杂度调整性能设置
          if (this.dragManager && result.data) {

            this.dragManager.updateDraggableObjectsCache(this.sceneObjects)

            // 显示优化信息
            const advice = interactionOptimizer.getOptimizationAdvice(result.data)
            if (advice && advice.suggestions.length > 0) {
              console.log('交互优化建议:', advice.suggestions.join(', '))
            }
          }

          // 清理临时URL
          URL.revokeObjectURL(pgmUrl)
          if (yamlUrl) {
            URL.revokeObjectURL(yamlUrl)
          }

          hideLoading()
          message.success('PGM地图渲染成功')
          console.log('PGM文件渲染完成:', result.data)
        } else {
          hideLoading()
          message.error('PGM渲染失败')
        }
      } catch (error) {
        hideLoading()
        message.error('PGM文件处理失败: ' + error.message)
        console.error('PGM文件处理错误:', error)
      }
    },

    // 显示保存地图模态框
    saveMap() {
      if (!this.currentMapId) {
        message.warning('请先选择一个地图')
        return
      }

      if (!this.currentMapData) {
        message.warning('当前地图数据不完整')
        return
      }

      // 设置可编辑的地图名称为当前地图名称
      this.editableMapName = this.currentMapData.name || ''
      // 显示保存地图模态框
      this.saveMapModalVisible = true
    },

    // 处理保存地图确认
    async handleSaveMapConfirm() {
      try {
        showLoading('正在保存地图信息...')

        // 收集当前地图的所有位置点信息
        const positionList = this.collectCurrentMapPositions()

        // 收集当前地图的所有路径信息
        const pathList = this.collectCurrentMapPaths()

        // 构建参数，如果有上传后的新文件名则使用新文件名，否则使用原文件名
        const params = {
          id: this.currentMapId,
          name: this.editableMapName, // 使用编辑后的地图名称
          pgmName: this.uploadedFileNames.pgm || this.currentMapData.pgmName,
          pcdName: this.uploadedFileNames.pcd || this.currentMapData.pcdName,
          yamlName: this.uploadedFileNames.yaml || this.currentMapData.yamlName,
          positionInformationRequests: positionList,
          pathRequests: pathList
        }

        console.log('准备发送的参数:', JSON.stringify(params, null, 2))

        const res = await saveMapInfo(params)

        hideLoading()

        if (res.errorCode === 0) {
          message.success('地图保存成功')

          // 将所有新建的位置元素标记为已保存
          this.markAllPositionsAsSaved()

          // 检查地图名称是否发生了变化
          const nameChanged = this.currentMapData.name !== this.editableMapName

          // 检查是否有新文件上传
          const hasNewFiles = this.uploadedFileNames.pgm || this.uploadedFileNames.pcd || this.uploadedFileNames.yaml

          // 更新当前地图数据中的名称和文件名
          this.currentMapData.name = this.editableMapName
          if (this.uploadedFileNames.pgm) {
            this.currentMapData.pgmName = this.uploadedFileNames.pgm
          }
          if (this.uploadedFileNames.pcd) {
            this.currentMapData.pcdName = this.uploadedFileNames.pcd
          }
          if (this.uploadedFileNames.yaml) {
            this.currentMapData.yamlName = this.uploadedFileNames.yaml
          }

          // 如果地图名称发生了变化，同步更新树形结构中的地图名称
          if (nameChanged) {
            this.updateTreeMapName(this.currentMapId, this.editableMapName)
          }

          // 注意：文件重新渲染已经在关闭文件管理模态框时完成，这里只需要更新文件名
          if (hasNewFiles) {
            console.log('保存地图时更新文件名，重新渲染已在文件管理模态框关闭时完成')
          }

          // 清空上传的文件名记录
          this.uploadedFileNames = {
            pgm: null,
            pcd: null,
            yaml: null
          }

          // 重新加载地图列表以确保数据同步
          await this.loadMapList()

          // 关闭模态框
          this.saveMapModalVisible = false
        } else {
          requestSuccess(res)
        }

      } catch (error) {
        hideLoading()
        requestFailed(error)
      }
    },

    // 处理保存地图取消
    handleSaveMapCancel() {
      this.saveMapModalVisible = false
      this.editableMapName = ''
    },

    // 将所有位置元素标记为已保存
    markAllPositionsAsSaved() {
      // 更新3D场景中的所有对象
      this.sceneObjects.forEach(obj => {
        if (!obj.isSaved) {
          obj.isSaved = true
        }
      })

      // 更新树形结构中的所有位置数据
      this.treeData.forEach(mapNode => {
        if (mapNode.children) {
          mapNode.children.forEach(positionNode => {
            if (positionNode.objectData && !positionNode.objectData.isSaved) {
              positionNode.objectData.isSaved = true
            }
          })
        }
      })

      // 如果当前有选中的位置数据，也更新它
      if (this.selectedPositionData && !this.selectedPositionData.isSaved) {
        this.selectedPositionData.isSaved = true
      }

      console.log('所有位置元素已标记为已保存')
    },

    // 更新树形结构中的地图名称
    updateTreeMapName(mapId, newName) {
      if (!mapId || !newName) return

      // 使用 TreeDataManager 更新地图名称
      const updated = this.treeDataManager.updateMapName(mapId, newName)

      if (updated) {
        // 同步更新本地树形数据
        this.treeData = this.treeDataManager.getTreeData()

      } else {
        console.warn(`未找到地图 ${mapId}，无法更新名称`)
      }
    },

    // 重新加载当前地图（用于文件更新后重新渲染）
    async reloadCurrentMap() {
      if (!this.currentMapId || !this.currentMapData) {
        console.warn('没有当前地图可以重新加载')
        return
      }
      showLoading('地图文件更新，正在重新渲染PGM地图...')
      try {
        // 创建更新后的地图数据，使用最新的文件名组合
        const updatedMapData = {
          ...this.currentMapData,
          // 如果有新上传的文件就用新的，没有就用原来的
          pgmName: this.uploadedFileNames.pgm || this.currentMapData.pgmName,
          yamlName: this.uploadedFileNames.yaml || this.currentMapData.yamlName,
          pcdName: this.uploadedFileNames.pcd || this.currentMapData.pcdName
        }

        console.log('使用更新后的文件名重新渲染地图:', {
          pgm: updatedMapData.pgmName,
          yaml: updatedMapData.yamlName,
          pcd: updatedMapData.pcdName
        })

        // 重新处理地图信息，使用更新后的文件名
        await this.processMapInfo(updatedMapData)

        hideLoading()
      } catch (error) {
        hideLoading()
        console.error('重新加载当前地图失败:', error)
        message.error('重新加载地图失败: ' + error.message)
      }
    },

    // 显示文件管理模态框
    showFileManageModal() {
      if (!this.currentMapId) {
        message.warning('请先选择一个地图')
        return
      }

      this.fileManageModalVisible = true
    },

    // 处理文件上传完成事件
    async handleFilesUploaded(uploadedFileNames) {
      console.log('检测到文件上传，重新渲染地图')

      // 更新上传的文件名
      Object.assign(this.uploadedFileNames, uploadedFileNames)

      // 立即更新 currentMapData 中对应的文件名
      if (uploadedFileNames.pgm) {
        this.currentMapData.pgmName = uploadedFileNames.pgm
      }
      if (uploadedFileNames.pcd) {
        this.currentMapData.pcdName = uploadedFileNames.pcd
      }
      if (uploadedFileNames.yaml) {
        this.currentMapData.yamlName = uploadedFileNames.yaml
      }

      // 重新渲染地图
      await this.reloadCurrentMap()
    },



    // 收集当前地图的所有位置点信息
    collectCurrentMapPositions() {
      return MapConfig.collectMapPositions(this.treeData, this.currentMapId, this.sceneObjects)
    },

    // 收集当前地图的所有路径信息
    collectCurrentMapPaths() {
      return MapConfig.collectMapPaths(this.treeData, this.currentMapId, this.pathSystemManager)
    },

    // 根据地图ID删除地图
    async deleteMap() {
      const _self = this
      if (_self.currentMapId) {
        Modal.confirm({
          title: `你确定要删除地图"${_self.currentMapData.name}"吗？`,
          content: '如果是的话请点击【确认按钮】，否则请点击【取消按钮】',
          okText: '确认',
          okType: 'danger',
          cancelText: '取消',
          async onOk() {
            showLoading('正在删除地图...')

            try {
              const res = await deleteMapById(_self.currentMapId)
              if (res.errorCode === 0) {
                message.success('地图删除成功')

                // 删除成功后的清理工作
                await _self.handleMapDeleteSuccess()
              } else {
                requestSuccess(res)
              }
              hideLoading()
            } catch (err) {
              hideLoading()
              requestFailed(err)
            }
          },
          onCancel() {
            console.log('Cancel');
          },
        });
      } else {
        message.warning('请先选择一个地图')
      }
    },

    // 处理地图删除成功后的清理工作
    async handleMapDeleteSuccess() {
      try {
        // 1. 清空当前地图相关数据
        this.currentMapId = null
        this.currentMapData = null
        this.selectedMapId = null

        // 2. 清空树形数据
        this.treeData = []
        this.expandedKeys = []
        this.selectedKeys = []

        // 3. 清空属性栏选中状态
        this.selectedPositionData = null
        this.selectedPathData = null
        this.selectedObject = null

        // 4. 清空3D场景内容
        this.clearSceneWithoutConfirm()

        // 5. 重置PCD渲染状态
        this.isPCDRendered = false

        // 6. 重置内容详情标签页
        this.contentDetailActiveTab = 'zh'

        // 7. 同步更新管理器状态
        if (this.treeDataManager) {
          this.treeDataManager.setTreeData([])
          this.treeDataManager.setExpandedKeys([])
          this.treeDataManager.setSelectedKeys([])
        }

        // 8. 重新查询地图列表
        await this.loadMapList()

        console.log('地图删除后的清理工作完成')
      } catch (error) {
        console.error('地图删除后清理工作失败:', error)
        message.error('清理工作失败: ' + error.message)
      }
    },

    // 删除选中的位置
    deleteSelectedPosition() {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除位置"${this.selectedPositionData.name}"吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.performDeletePosition()
        }
      })
    },

    // 执行删除位置操作
    async performDeletePosition() {
      if (!this.selectedPositionData) return

      // 判断是否为新建位置（通过检查isSaved状态来判断）
      const isNewPosition = !this.selectedObject || !this.selectedObject.isSaved

      if (isNewPosition) {
        // 新建位置，直接执行本地删除操作，不需要调用接口
        this.performLocalDelete()
        message.success('位置删除成功')
      } else {
        // 已保存的位置，需要先调用删除接口
        try {
          showLoading('正在删除位置...')

          // 构建删除参数，使用uuid
          const deleteParams = {
            mapId: this.currentMapId,
            uuid: this.selectedObject.uuid
          }

          // 先调用删除接口
          const res = await deletePosition(deleteParams)

          if (res.errorCode === 0) {
            // 接口删除成功，执行本地删除操作
            this.performLocalDelete()
            hideLoading()
            message.success('位置删除成功')
          } else {
            // 接口删除失败，显示错误信息
            hideLoading()
            requestSuccess(res)
          }
        } catch (error) {
          hideLoading()
          requestFailed(error)
        }
      }
    },

    // 执行本地删除操作（从3D场景和树形结构中删除）
    performLocalDelete() {
      if (!this.selectedPositionData) return

      console.log('开始执行本地删除操作:', this.selectedPositionData.name)

      // 从3D场景中删除对应对象
      // 优先通过uuid匹配，避免重名问题
      let sceneObjectIndex = -1
      if (this.selectedPositionData.uuid) {
        sceneObjectIndex = this.sceneObjects.findIndex(obj => obj.uuid === this.selectedPositionData.uuid)
      }

      // 如果没有uuid或未找到，则通过当前选中的对象匹配
      if (sceneObjectIndex === -1 && this.selectedObject) {
        sceneObjectIndex = this.sceneObjects.findIndex(obj => obj === this.selectedObject)
      }

      if (sceneObjectIndex !== -1) {
        const object = this.sceneObjects[sceneObjectIndex]
        console.log('从3D场景删除对象:', object.name)
        this.scene.remove(object.mesh)
        this.sceneObjects.splice(sceneObjectIndex, 1)

        // 更新拖拽管理器的可拖拽对象缓存
        if (this.dragManager) {
          this.dragManager.updateDraggableObjectsCache(this.sceneObjects)
        }
      } else {
        console.warn('未找到要删除的3D对象，uuid:', this.selectedPositionData.uuid)
      }

      // 清空所有引用该位置的路径的位置引用
      this.clearPathReferencesToPosition(this.selectedPositionData.uuid)

      // 从树形数据中删除，使用更精确的匹配方式
      const removed = this.treeDataManager.removePositionByData(this.currentMapId, this.selectedPositionData)
      if (removed) {
        this.treeData = this.treeDataManager.getTreeData()
        console.log('树形结构删除成功')
      } else {
        console.warn('树形结构删除失败')
      }

      // 清空选择
      this.selectedPositionData = null
      this.selectedObject = null
      this.selectedKeys = []
      this.treeDataManager.setSelectedKeys([])
    },

    // 删除选中的路径
    deleteSelectedPath() {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除路径"${this.selectedPathData.name}"吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.performDeletePath()
        }
      })
    },

    // 执行删除路径操作
    async performDeletePath() {
      const _self = this
      if (!_self.selectedPathData) return

      const pathUuid = _self.selectedPathData.uuid
      const pathName = _self.selectedPathData.name
      const isSaved = _self.selectedPathData.isSaved

      console.log(`开始删除路径: ${pathName}, UUID: ${pathUuid}, 已保存: ${isSaved}`)

      try {
        // 如果是已保存的路径，先调用API删除
        if (isSaved) {
          console.log(`路径 ${pathName} 是已保存的路径，先调用API删除`)
          showLoading('正在删除路径...')

          try {
            const info = {
              uuid: pathUuid,
              mapId: _self.selectedMapId
            }

            const res = await deletePath(info)

            if (res.errorCode === 0) {

              // 执行本地删除
              _self.performLocalDeletePath(pathUuid, pathName)

            } else {
              requestSuccess(res)
            }
          } catch (error) {
            hideLoading()
            console.error(`删除路径API调用失败:`, error)
            return
          }

          hideLoading()
        } else {
          // 执行本地删除
          _self.performLocalDeletePath(pathUuid, pathName)
        }

      } catch (error) {
        hideLoading()
        console.error(`删除路径时出错:`, error)
      }
    },

    // 执行本地删除路径操作
    performLocalDeletePath(pathUuid, pathName) {
      // 获取路径对象
      const pathObject = this.pathSystemManager.getPath(pathUuid)
      if (!pathObject || !pathObject.group) {
        console.error(`未找到路径对象: ${pathUuid}`)
        return
      }

      console.log(`开始本地删除路径: ${pathName}, group子对象数量: ${pathObject.group.children.length}`)

      //  步骤1: 先从sceneObjects数组中移除（使用对象引用比较）
      const sceneObjectIndex = this.sceneObjects.findIndex(obj => obj === pathObject.group)
      if (sceneObjectIndex !== -1) {
        this.sceneObjects.splice(sceneObjectIndex, 1)
        console.log(`从sceneObjects数组中移除路径对象`)
      } else {
        console.warn(`在sceneObjects中未找到路径组对象`)
      }

      //  步骤2: 彻底清理路径组的所有3D内容
      pathObject.group.visible = false // 先设置为不可见，立即停止渲染

      // 清空group的所有子对象
      const childrenToRemove = [...pathObject.group.children]
      childrenToRemove.forEach(child => {
        pathObject.group.remove(child)
        if (child.geometry) child.geometry.dispose()
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => mat.dispose())
          } else {
            child.material.dispose()
          }
        }
      })

      // 步骤3: 从场景中彻底移除路径组
      this.scene.remove(pathObject.group)

      // 递归查找并移除任何残留引用
      const toRemove = []
      this.scene.traverse((child) => {
        if (child === pathObject.group) {
          toRemove.push({ child, parent: child.parent })
        }
      })

      toRemove.forEach(({ child, parent }) => {
        if (parent && parent !== this.scene) {
          parent.remove(child)
        }
      })

      // 步骤4: 从路径系统管理器中删除（这会调用dispose）
      this.pathSystemManager.removePath(pathUuid)

      // 从树形数据中删除
      const removed = this.treeDataManager.removePathByData(this.currentMapId, this.selectedPathData)
      if (removed) {
        this.treeData = this.treeDataManager.getTreeData()
      }

      // 清空选择状态
      this.selectedPathData = null
      this.selectedObject = null
      this.selectedKeys = []
      this.treeDataManager.setSelectedKeys([])

      // 步骤5: 最终强制渲染更新
      if (this.renderer) {
        this.renderer.render(this.scene, this.camera)
      }

      console.log(`路径 ${pathName} 本地删除完成`)
      message.success('路径删除成功')
    },

    // 异步加载树节点数据（现在只是占位，实际加载在点击时进行）
    async onLoadData(treeNode) {
      console.log('onLoadData', treeNode)
      // 如果已经有子节点，直接返回
      if (treeNode.dataRef.children) {
        return
      }

      // 初始化为空数组，等待用户点击时加载
      treeNode.dataRef.children = []
      this.treeData = [...this.treeData]
    },

    // 切换PCD渲染状态
    async togglePCD() {
      if (this.isPCDRendered) {
        this.clearPCD()
      } else {
        await this.downloadAndRenderPCD()
      }
    },

    // 下载并渲染PCD文件
    async downloadAndRenderPCD() {
      if (!this.currentMapData || !this.currentMapData.pcdName) {
        message.warning('当前地图没有PCD文件')
        return
      }

      try {
        showLoading('正在渲染PCD地图...')

        // 调用下载接口获取PCD文件
        const pcdResponse = await downloadMapFile(this.currentMapData.id, this.currentMapData.pcdName)
        console.log('PCD文件下载成功:', pcdResponse)

        if (pcdResponse) {
          // 接口返回的直接就是blob数据，创建URL
          const pcdUrl = URL.createObjectURL(pcdResponse)

          // 使用PCDRenderer渲染PCD文件，PCD文件已经是米单位，不需要小缩放
          const result = await this.pcdRenderer.renderPCD(pcdUrl, 1.0, 0.05)

          // 调整相机位置以适应PCD点云
          this.adjustCameraForPCD(result.bounds)

          // 限制现有位置元素在PCD范围内
          this.clampExistingPositionsToPCD()

          // 清理临时URL
          URL.revokeObjectURL(pcdUrl)

          // 更新状态
          this.isPCDRendered = true

          hideLoading()
        } else {
          hideLoading()
          message.error('PCD文件加载失败')
        }
      } catch (error) {
        hideLoading()
        message.error('PCD文件处理失败: ' + error.message)
        console.error('PCD文件处理错误:', error)
      }
    },

    // 清除PCD渲染
    clearPCD() {
      if (this.pcdRenderer) {
        this.pcdRenderer.dispose()

        // 重置拖拽管理器边界
        this.resetDragManagerBounds()

        // 重置相机位置
        this.resetCameraPosition()

        // 更新状态
        this.isPCDRendered = false

        message.success('已清除PCD点云')
      }
    },

    // 调整相机位置以适应PCD点云
    adjustCameraForPCD(bounds) {
      if (!bounds || !this.camera || !this.controls) return

      // 计算合适的相机距离
      const maxDimension = Math.max(bounds.width, bounds.height, bounds.depth)
      const distance = maxDimension * 2 // 相机距离为点云最大尺寸的2倍

      // 设置相机位置（斜上方视角，Z轴向上）
      this.camera.position.set(
        bounds.centerX + distance * 0.5,
        bounds.centerY - distance * 0.8,
        bounds.centerZ + distance * 0.5
      )
      this.camera.up.set(0, 0, 1) // 确保up向量为Z轴向上
      this.camera.lookAt(bounds.centerX, bounds.centerY, bounds.centerZ)

      // 更新控制器目标点
      this.controls.target.set(bounds.centerX, bounds.centerY, bounds.centerZ)
      this.controls.update()

      console.log(`相机已调整到PCD点云视角，距离: ${distance}`)
    },

    // 限制现有位置元素在PCD范围内
    clampExistingPositionsToPCD() {
      if (!this.pcdRenderer || !this.pcdRenderer.hasPCD()) return

      let clampedCount = 0
      const updatedPositions = [] // 记录被更新的位置UUID

      this.sceneObjects.forEach(obj => {
        if (obj.type === 'person') {
          const originalPos = { x: obj.position.x, y: obj.position.y, z: obj.position.z }
          // PCD边界限制（XY平面）
          const clampedPoint = this.pcdRenderer.clampPointToPCDBounds(originalPos.x, originalPos.y)
          const clampedPos = {
            x: clampedPoint.x,
            y: clampedPoint.y, // Y坐标可以变化
            z: originalPos.z    // Z坐标保持不变（高度）
          }

          // 如果位置发生了变化，更新对象位置
          if (clampedPos.x !== originalPos.x || clampedPos.y !== originalPos.y) {
            obj.position.copy(clampedPos)
            if (obj.mesh) {
              obj.mesh.position.copy(clampedPos)
            }
            clampedCount++

            // 同步更新位置数据
            this.updatePositionDataFromObject(obj)

            // 记录被更新的位置UUID，稍后更新相关路径
            if (obj.uuid) {
              updatedPositions.push(obj.uuid)
            }
          }
        }
      })

      // 更新所有受影响位置的相关路径
      updatedPositions.forEach(positionUuid => {
        this.updatePathsReferencingPosition(positionUuid)
      })

      if (clampedCount > 0) {
        console.log(`已将 ${clampedCount} 个位置元素限制在PCD范围内`)
        message.info(`已调整 ${clampedCount} 个位置元素到点云范围内`)
      }
    },

    // 更新PCD渲染器的边界
    updateDragManagerBoundsForPCD(bounds) {
      if (this.dragManager && bounds) {
        this.dragManager.setBounds({
          minX: bounds.minX,
          maxX: bounds.maxX,
          minY: bounds.minY,
          maxY: bounds.maxY,
          minZ: 0 // Z轴最小值保持为0（地面）
        })
        console.log('拖拽边界已更新为PCD范围:', bounds)
      }
    },

    // 清除PGM渲染
    clearPGM() {
      if (this.pgmRenderer) {
        this.pgmRenderer.dispose()

        // 恢复默认地板和网格
        SceneSetup.showDefaultFloor(this.scene)

        // 重置拖拽管理器边界
        this.resetDragManagerBounds()

        // 重置相机位置
        this.resetCameraPosition()

        message.success('已清除PGM地图，恢复默认地板和拖拽范围')
        console.log('PGM地图已清除')
      }
    },

    // 重置相机位置
    resetCameraPosition() {
      if (this.camera && this.controls) {
        this.camera.position.set(0, -15, 10)
        this.camera.up.set(0, 0, 1) // 确保up向量为Z轴向上
        this.camera.lookAt(0, 0, 0)
        this.controls.target.set(0, 0, 0)
        this.controls.update()
      }
    },

    // 更新拖拽管理器的边界
    updateDragManagerBounds(bounds) {
      if (this.dragManager && bounds) {
        this.dragManager.setBounds({
          minX: bounds.minX,
          maxX: bounds.maxX,
          minY: bounds.minY,
          maxY: bounds.maxY,
          minZ: 0 // Z轴最小值保持为0（地面）
        })
        console.log('拖拽边界已更新为PGM范围:', bounds)
      }
    },

    // 重置拖拽管理器边界为默认值
    resetDragManagerBounds() {
      if (this.dragManager) {
        this.dragManager.resetBounds()
        console.log('拖拽边界已重置为默认值')
      }
    },

    // 调整相机位置以适应PGM地图
    adjustCameraForPGM(bounds) {
      if (!bounds || !this.camera || !this.controls) return

      // 计算合适的相机距离
      const maxDimension = Math.max(bounds.width, bounds.height)
      const distance = maxDimension * 1.5 // 相机距离为地图最大尺寸的1.5倍

      // 设置相机位置（俯视角度，Z轴向上，基于PGM中心）
      this.camera.position.set(
        bounds.centerX,
        bounds.centerY - distance * 0.8,
        distance * 0.6
      )
      this.camera.up.set(0, 0, 1) // 确保up向量为Z轴向上
      this.camera.lookAt(bounds.centerX, bounds.centerY, 0)

      // 更新控制器目标点
      this.controls.target.set(bounds.centerX, bounds.centerY, 0)
      this.controls.update()

      console.log(`相机已调整到PGM地图视角，距离: ${distance}`)
    },

    // 限制现有位置元素在PGM范围内
    clampExistingPositionsToPGM() {
      if (!this.pgmRenderer || !this.pgmRenderer.hasPGM()) return

      let clampedCount = 0
      const updatedPositions = [] // 记录被更新的位置UUID

      this.sceneObjects.forEach(obj => {
        if (obj.type === 'person') {
          const originalPos = { x: obj.position.x, y: obj.position.y, z: obj.position.z }
          const clampedPos = this.positionManager.validateAndClampPosition(originalPos, this.pgmRenderer)

          // 如果位置发生了变化，更新对象位置（检查Y轴变化）
          if (clampedPos.x !== originalPos.x || clampedPos.y !== originalPos.y) {
            obj.position.copy(clampedPos)
            if (obj.mesh) {
              obj.mesh.position.copy(clampedPos)
            }
            clampedCount++

            // 同步更新位置数据
            this.updatePositionDataFromObject(obj)

            // 记录被更新的位置UUID，稍后更新相关路径
            if (obj.uuid) {
              updatedPositions.push(obj.uuid)
            }
          }
        }
      })

      // 更新所有受影响位置的相关路径
      updatedPositions.forEach(positionUuid => {
        this.updatePathsReferencingPosition(positionUuid)
      })

      if (clampedCount > 0) {
        console.log(`已将 ${clampedCount} 个位置元素限制在PGM范围内`)
        message.info(`已调整 ${clampedCount} 个位置元素到地图范围内`)
      }
    },

    // 从3D对象更新位置数据
    updatePositionDataFromObject(object) {
      if (!object || !this.currentMapId) return

      // 查找对应的树节点
      const treeNode = this.treeDataManager.findTreeNodeByObject(object, this.currentMapId)
      if (treeNode && treeNode.objectData) {
        // 更新树节点中的位置数据，保留两位小数
        treeNode.objectData.xcoordinate = (Math.round(object.position.x * 100) / 100).toString()
        treeNode.objectData.ycoordinate = (Math.round(object.position.y * 100) / 100).toString()
        treeNode.objectData.zcoordinate = (Math.round(object.position.z * 100) / 100).toString()

        // 如果当前选中的是这个对象，也更新选中的位置数据，保留两位小数
        if (this.selectedObject === object && this.selectedPositionData) {
          this.selectedPositionData.xcoordinate = (Math.round(object.position.x * 100) / 100).toString()
          this.selectedPositionData.ycoordinate = (Math.round(object.position.y * 100) / 100).toString()
          this.selectedPositionData.zcoordinate = (Math.round(object.position.z * 100) / 100).toString()
        }
      }
    },

    // 根据mapId查询位置点数据（现在通过getMapInfo获取）
    async fetchPositionsByMapId(mapId) {
      try {
        const res = await getMapInfo(mapId)
        if (res.errorCode === 0 && res.data && res.data.positionInformations) {
          return res.data.positionInformations
        }
        return []
      } catch (error) {
        console.error('获取位置数据失败:', error)
        return []
      }
    },

    // 将位置点数据转换为3D场景对象格式
    convertPositionDataToSceneObjects(positionData) {
      return positionData.map(pos => {
        const rotationRadians = (parseFloat(pos.yaw) || 0) * Math.PI / 180
        console.log(`转换位置数据 ${pos.name}: yaw=${pos.yaw}度 -> rotation=${rotationRadians}弧度`)

        return {
          name: pos.name,
          type: 'person', // 默认类型为person
          position: {
            x: parseFloat(pos.xcoordinate) || 0,
            y: parseFloat(pos.ycoordinate) || 0,
            z: parseFloat(pos.zcoordinate) || 0
          },
          rotation: rotationRadians, // 将角度转换为弧度
          actionNo: pos.actionNo || '', // 保留动作编号
          contentDetailZh: pos.contentDetailZh || '', // 中文内容
          contentDetailEn: pos.contentDetailEn || '', // 英文内容
          contentDetailJa: pos.contentDetailJa || '', // 日文内容
          contentDetailKo: pos.contentDetailKo || '', // 韩文内容
          uuid: pos.uuid, // 传递uuid
          isSaved: true // 从服务器加载的数据标记为已保存
        }
      })
    },

    // 树节点展开事件
    async onTreeExpand(expandedKeys, { expanded, node }) {
      // 直接同步展开状态，让ant-design-vue的树组件自己处理展开/折叠
      this.expandedKeys = expandedKeys
      this.treeDataManager.setExpandedKeys(expandedKeys)

      console.log(`树节点${expanded ? '展开' : '折叠'}: ${node.title}, key: ${node.key}`)

      if (expanded && !node.isLeaf) {
        // 展开节点
        if (node.objectType === 'category') {
          // 这是分类节点（位置或路径），只需要记录日志
          console.log(`展开分类节点: ${node.title}`)
        } else {
          // 这是地图节点，需要确保3D场景加载了对应的地图
          const mapId = parseInt(node.key) || node.key
          if (this.currentMapId !== mapId) {
            await this.loadMap(mapId)
          }
        }
      } else if (!expanded && !node.isLeaf) {
        // 折叠节点
        console.log(`折叠节点: ${node.title}`)

        // 如果折叠的是分类节点，清空相关选择状态
        if (node.objectType === 'category') {
          // 如果当前选中的是该分类下的子项，清空选择
          if (this.selectedKeys.length > 0) {
            const selectedKey = this.selectedKeys[0]
            const categoryKey = node.key

            // 检查选中的项是否属于当前折叠的分类
            let shouldClearSelection = false
            if (categoryKey.endsWith('_positions') && selectedKey.includes('_position_')) {
              const mapId = categoryKey.replace('_positions', '')
              shouldClearSelection = selectedKey.startsWith(`${mapId}_position_`)
            } else if (categoryKey.endsWith('_paths') && selectedKey.includes('_path_')) {
              const mapId = categoryKey.replace('_paths', '')
              shouldClearSelection = selectedKey.startsWith(`${mapId}_path_`)
            }

            if (shouldClearSelection) {
              this.selectedKeys = []
              this.selectedPositionData = null
              this.selectedPathData = null
              this.selectedObject = null
              this.treeDataManager.setSelectedKeys([])
              console.log(`清空了分类 ${node.title} 下的选择状态`)
            }
          }
        }
      }
    },

    // 处理树节点标题点击事件
    handleTitleClick(key, objectType, isLeaf, event) {
      // 只处理分类节点的标题点击，叶子节点让其正常触发选择事件
      if (!isLeaf && objectType === 'category') {
        // 阻止事件冒泡，避免触发树节点的选择事件
        event.stopPropagation()

        // 切换展开/折叠状态
        const isExpanded = this.expandedKeys.includes(key)

        if (isExpanded) {
          // 当前是展开状态，执行折叠
          this.expandedKeys = this.expandedKeys.filter(k => k !== key)
          console.log(`通过标题点击折叠分类节点: ${key}`)

          // 如果当前选中的是该分类下的子项，清空选择
          if (this.selectedKeys.length > 0) {
            const selectedKey = this.selectedKeys[0]
            let shouldClearSelection = false

            if (key.endsWith('_positions') && selectedKey.includes('_position_')) {
              const mapId = key.replace('_positions', '')
              shouldClearSelection = selectedKey.startsWith(`${mapId}_position_`)
            } else if (key.endsWith('_paths') && selectedKey.includes('_path_')) {
              const mapId = key.replace('_paths', '')
              shouldClearSelection = selectedKey.startsWith(`${mapId}_path_`)
            }

            if (shouldClearSelection) {
              this.selectedKeys = []
              this.selectedPositionData = null
              this.selectedPathData = null
              this.selectedObject = null
              this.treeDataManager.setSelectedKeys([])
            }
          }
        } else {
          // 当前是折叠状态，执行展开
          this.expandedKeys.push(key)
          console.log(`通过标题点击展开分类节点: ${key}`)
        }

        // 同步状态到TreeDataManager
        this.treeDataManager.setExpandedKeys(this.expandedKeys)
      }
      // 叶子节点的点击不做任何处理，让其正常触发树组件的选择事件
    },

    // 根据key查找树节点（三级结构）
    findTreeNodeByKey(key) {
      for (const mapNode of this.treeData) {
        if (mapNode.key === key) {
          return mapNode
        }
        if (mapNode.children) {
          for (const categoryNode of mapNode.children) {
            if (categoryNode.key === key) {
              return categoryNode
            }
            // 查找分类节点的子节点
            if (categoryNode.children) {
              for (const leafNode of categoryNode.children) {
                if (leafNode.key === key) {
                  return leafNode
                }
              }
            }
          }
        }
      }
      return null
    },

    // 树节点选择事件
    async onTreeSelect(selectedKeys, info) {
      console.log('树节点选择事件:', selectedKeys, info)

      // 同步选中状态到组件和 treeDataManager
      this.selectedKeys = selectedKeys
      this.treeDataManager.setSelectedKeys(selectedKeys)

      if (selectedKeys.length === 0) {
        // 清空选择，同步取消3D场景中的选中状态
        this.selectObject(null) // 这会同时清空selectedObject和selectedPositionData
        this.currentSelectedMapId = null
        return
      }

      const selectedKey = selectedKeys[0]
      console.log('树形选择事件 - selectedKey:', selectedKey)

      // 🔥 关键修复：直接通过selectedKey查找对应的树节点，而不是使用info.selectedNodes[0]
      const selectedNode = this.findTreeNodeByKey(selectedKey)

      if (!selectedNode) {
        console.error(`未找到key为 ${selectedKey} 的树节点`)
        return
      }

      if (selectedNode.isLeaf) {
        // 根据对象类型处理不同的叶子节点
        const objectType = selectedNode.objectType || 'position' // 默认为位置类型

        if (objectType === 'path') {
          // 选中的是路径节点
          this.selectedPositionData = null
          this.selectedPathData = { ...selectedNode.objectData }
        } else {
          // 选中的是位置节点
          console.log('树形选择：位置节点', selectedNode.objectData)

          // 清空路径数据，设置位置数据
          this.selectedPathData = null
          this.selectedPositionData = { ...selectedNode.objectData } // 复制数据以便编辑
        }

        // 设置当前选中的地图ID
        const { mapId } = selectedNode
        this.currentSelectedMapId = mapId

        // 确保地图已加载，如果没有加载则先加载
        if (this.currentMapId !== mapId) {
          await this.loadMap(mapId)
        }

        // 在3D场景中选中对应的对象
        // 优先通过uuid查找，其次通过树节点key精确匹配
        if (selectedNode.objectData && selectedNode.objectData.uuid) {
          console.log(`通过uuid选择对象: ${selectedNode.objectData.uuid}`)
          this.selectObjectByUUID(selectedNode.objectData.uuid)
        } else {
          console.log('objectData没有uuid，使用树节点key匹配')
          // 使用树节点的key来精确匹配对象
          this.selectObjectByTreeNodeKey(selectedNode.key, selectedNode.objectData)
        }

        // 强制更新树形组件的选中状态
        this.$nextTick(() => {
          this.selectedKeys = [selectedKey]
        })
      } else {
        // 选中的是地图节点或分类节点
        this.selectObject(null) // 取消3D对象选中
        this.selectedPositionData = null // 清空位置数据选择
        this.selectedPathData = null // 清空路径数据选择

        // 判断是地图节点还是分类节点
        if (selectedNode.objectType === 'category') {
          // 选中的是分类节点（位置或路径）
          this.currentSelectedMapId = selectedNode.mapId

          // 自动展开选中的分类节点
          if (!this.expandedKeys.includes(selectedKey)) {
            this.expandedKeys.push(selectedKey)
          }
        } else {
          // 选中的是地图节点
          this.currentSelectedMapId = selectedKey

          // 如果当前显示的不是这个地图，才需要重新加载
          const mapId = parseInt(selectedKey) || selectedKey
          if (this.currentMapId !== mapId) {
            await this.loadMap(mapId)
          }

          // 自动展开选中的地图节点
          if (!this.expandedKeys.includes(selectedKey)) {
            this.expandedKeys.push(selectedKey)
          }
        }
      }
    },

    // 根据uuid在3D场景中选中对象
    selectObjectByUUID(uuid) {
      if (!uuid) return

      // 先在sceneObjects中查找（位置对象）
      let targetObject = this.sceneObjects.find(obj => obj.uuid === uuid)

      // 如果没找到，查找路径对象（Group类型，通过userData.objectUuid匹配）
      if (!targetObject) {
        targetObject = this.sceneObjects.find(obj =>
          obj.type === 'Group' &&
          obj.userData &&
          obj.userData.objectUuid === uuid
        )
      }

      if (targetObject) {
        this.selectObject(targetObject)
        console.log(`已通过uuid选中对象: ${uuid}`)
      } else {
        console.warn(`未找到uuid为 ${uuid} 的3D对象`)
      }
    },

    // 根据树节点key精确选中对象（处理重名情况）
    selectObjectByTreeNodeKey(nodeKey, objectData) {
      if (!nodeKey || !objectData) return

      // 首先尝试通过uuid匹配
      if (objectData.uuid) {
        const targetObject = this.sceneObjects.find(obj => obj.uuid === objectData.uuid)
        if (targetObject) {
          this.selectObject(targetObject)
          console.log(`已通过uuid选中对象: ${targetObject.name}`)
          return
        }
      }

      // 如果通过uuid未找到对象，说明数据不一致
      console.warn(`未找到匹配的3D对象，nodeKey: ${nodeKey}, uuid: ${objectData.uuid || 'undefined'}`)
    },

    // 加载地图并将位置点添加为子级（现在通过getMapInfo接口）
    async loadMapWithPositions(mapId) {
      try {
        // 调用 getMapInfo 接口获取地图详细信息
        const res = await getMapInfo(mapId)
        if (res.errorCode === 0) {
          // 处理地图信息和位置信息
          await this.processMapInfo(res.data)
          console.log(`已加载地图: ${mapId} 及其位置点数据`)
        } else {
          requestSuccess(res)
        }
      } catch (error) {
        console.error('加载地图和位置点数据失败:', error)
        requestFailed(error)
      }
    },

    // 加载地图数据
    async loadMap(mapId) {
      const mapData = this.mapList.find(map => map.id === mapId)
      if (!mapData) return

      this.currentMapId = mapId

      // 清空当前场景
      this.clearSceneWithoutConfirm()

      try {
        // 获取该地图的位置点数据
        const positionData = await this.fetchPositionsByMapId(mapId)

        if (positionData && positionData.length > 0) {
          // 将位置点数据转换为3D场景可用的格式
          const sceneObjects = this.convertPositionDataToSceneObjects(positionData)
          // 导入位置点数据到场景中
          this.importObjects(sceneObjects)
        }

        console.log(`已加载地图: ${mapData.name}`)
      } catch (error) {
        console.error('加载地图数据失败:', error)
      }
    },



    initThreeJS() {
      const container = this.$refs.threeContainer

      // 使用SceneSetup初始化场景
      const sceneConfig = SceneSetup.initThreeJS(container)
      this.scene = sceneConfig.scene
      this.camera = sceneConfig.camera
      this.renderer = sceneConfig.renderer
      this.controls = sceneConfig.controls

      // 初始化PGM渲染器
      this.pgmRenderer = new PGMRenderer(this.scene)

      // 初始化PCD渲染器
      this.pcdRenderer = new PCDRenderer(this.scene)

      // 添加光源
      SceneSetup.setupLighting(this.scene)

      // 添加地面
      SceneSetup.createFloor(this.scene)

      // 初始化拖拽管理器
      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement))
      this.dragManager.setControls(this.controls)
      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate)

      // 初始化路径交互管理器
      this.pathInteractionManager = new PathInteractionManager(
        this.scene,
        this.camera,
        this.renderer,
        this.pathSystemManager
      )

      // 设置轨道控制器引用
      this.pathInteractionManager.setControls(this.controls)

      // 设置路径选中回调
      this.pathInteractionManager.setPathSelectCallback((pathObject) => {
        if (pathObject) {
          this.selectObject(pathObject.group)
        } else {
          this.selectObject(null)
        }
      })

      // 设置事件监听
      SceneSetup.setupWindowResize(this.camera, this.renderer, container)
      SceneSetup.setupClickHandler(this.renderer, this.camera, () => this.sceneObjects, this.selectObject.bind(this), container, this.pathInteractionManager)
      SceneSetup.setupWheelHandler(this.renderer, this.onCanvasWheel)

      // 启用PathInteractionManager处理拖拽和双击事件（不再监听click事件，由SceneSetup统一处理）
      this.pathInteractionManager.enable(container)
    },



    // 设置拖拽元素类型
    startDrag(type, event) {
      event.dataTransfer.setData('text/plain', type)
    },

    // 拖放事件
    setupDragAndDrop() {
      const container = this.$refs.threeContainer
      SceneSetup.setupDragAndDrop(container, this.createObjectAtPosition.bind(this))
    },

    // 创建对象并放置在指定位置
    createObjectAtPosition(type, event) {
      // 计算地面交点
      let intersectPoint = this.positionManager.calculateGroundIntersection(
        event,
        this.$refs.threeContainer,
        this.camera
      )

      // 优先使用PGM边界限制，其次使用PCD边界限制（Y轴水平）
      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        const clampedPoint = this.pgmRenderer.clampPointToPGMBounds(intersectPoint.x, intersectPoint.y)
        intersectPoint.x = clampedPoint.x
        intersectPoint.y = clampedPoint.y
      } else if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        const clampedPoint = this.pcdRenderer.clampPointToPCDBounds(intersectPoint.x, intersectPoint.y)
        intersectPoint.x = clampedPoint.x
        intersectPoint.y = clampedPoint.y
      }

      // 根据类型创建不同的对象
      if (type === 'straightPath' || type === 'bezierPath') {
        this.createPathAtPosition(type, intersectPoint)
      } else {
        // 创建位置对象
        this.createObjectAndAddToTree(type, intersectPoint)
      }
    },

    // 创建对象并添加到树形结构
    createObjectAndAddToTree(type, position) {
      // 如果没有选中的地图，使用当前加载的地图
      const targetMapId = this.currentSelectedMapId || this.currentMapId

      if (!targetMapId) {
        message.warning('请先打开一个地图')
        return
      }

      // 获取当前地图中已有的所有位置信息
      const existingPositions = this.getCurrentMapPositions(targetMapId)

      // 使用PositionManager创建位置数据，传入已有位置信息以避免重名
      const { positionData, objectData } = this.positionManager.createPositionData(targetMapId, position, existingPositions)

      // 创建3D对象
      this.createObject(type, position, objectData)

      // 添加到树形结构
      const newNodeKey = this.treeDataManager.addPositionToTree(targetMapId, positionData)

      // 同步树形数据
      this.treeData = this.treeDataManager.getTreeData()
      this.expandedKeys = this.treeDataManager.getExpandedKeys()

      // 自动选中新添加的位置元素
      this.selectNewlyAddedPosition(newNodeKey, positionData)

      console.log(`已创建位置点并添加到地图 ${targetMapId}: ${positionData.name}`)
    },

    // 创建路径并放置在指定位置
    createPathAtPosition(type, position) {
      const targetMapId = this.currentSelectedMapId || this.currentMapId
      if (!targetMapId) {
        message.warning('请先打开一个地图')
        return
      }

      // 生成路径名称和创建路径对象
      const pathType = type === 'straightPath' ? 'straight' : 'bezier'
      const pathName = this.pathSystemManager.generateUniquePathName(targetMapId, pathType)
      const endPoint = { x: position.x + 2, y: position.y + 2, z: 0.1 }  // 在XY平面上创建终点

      const pathObject = type === 'straightPath'
        ? new StraightPath(pathName, targetMapId, position, endPoint)
        : new BezierPath(pathName, targetMapId, position, endPoint)

      if (!pathObject) return

      // 设置地图复杂度优化
      if (this.pgmRenderer?.pgmData) {
        const mapComplexity = this.pgmRenderer.pgmData.width * this.pgmRenderer.pgmData.height
        pathObject.setMapComplexity(mapComplexity)
      }

      // 添加到系统和场景
      this.pathSystemManager.addPath(pathObject)
      this.scene.add(pathObject.group)
      this.sceneObjects.push(pathObject.group)

      // 添加到树形结构并自动选中
      const pathData = pathObject.getPathData()
      const newNodeKey = this.treeDataManager.addPathToTree(targetMapId, pathData)

      this.treeData = this.treeDataManager.getTreeData()
      this.expandedKeys = this.treeDataManager.getExpandedKeys()
      this.selectNewlyAddedPath(newNodeKey, pathData)
    },

    // 获取当前地图中已有的所有位置信息
    getCurrentMapPositions(mapId) {
      const positions = []

      // 从树形数据中获取位置信息（三级结构）
      const mapNode = this.treeData.find(node => node.key === mapId.toString() || node.key === mapId)
      if (mapNode && mapNode.children) {
        // 查找位置分类节点
        const positionsNode = mapNode.children.find(child =>
          child.categoryType === 'positions' && child.objectType === 'category'
        )

        if (positionsNode && positionsNode.children) {
          positionsNode.children.forEach(child => {
            if (child.objectData && child.objectType === 'position') {
              positions.push({
                name: child.objectData.name,
                uuid: child.objectData.uuid
              })
            }
          })
        }
      }

      return positions
    },

    // 根据已有位置更新位置计数器
    updatePositionCounterFromExistingPositions(mapId, existingPositions) {
      if (!existingPositions || existingPositions.length === 0) {
        return
      }

      // 找出所有符合"位置X"格式的名称，并提取最大的数字
      let maxNumber = 0
      existingPositions.forEach(position => {
        const match = position.name.match(/^位置(\d+)$/)
        if (match) {
          const number = parseInt(match[1])
          if (number > maxNumber) {
            maxNumber = number
          }
        }
      })

      // 设置位置计数器为最大数字+1，确保新建位置不会重名
      if (maxNumber > 0) {
        this.positionManager.setPositionCounter(mapId, maxNumber + 1)
        console.log(`已更新地图 ${mapId} 的位置计数器为: ${maxNumber + 1}`)
      }
    },

    // 自动选中新添加的位置元素
    selectNewlyAddedPosition(nodeKey, positionData) {
      if (!nodeKey) return

      // 使用nextTick确保DOM更新完成后再选中
      this.$nextTick(() => {
        // 设置选中的节点
        this.selectedKeys = [nodeKey]
        this.treeDataManager.setSelectedKeys(this.selectedKeys)

        // 设置位置数据到属性面板
        this.selectedPositionData = { ...positionData }

        // 设置当前选中的地图ID
        const [mapId] = nodeKey.split('_')
        this.currentSelectedMapId = mapId

        // 在3D场景中选中对应的对象
        // 对于新建的位置元素，通过uuid查找
        this.selectObjectByUUID(positionData.uuid)

        console.log(`已自动选中新添加的位置: ${positionData.name}`)
      })
    },

    // 自动选中新添加的路径元素
    selectNewlyAddedPath(nodeKey, pathData) {
      if (!nodeKey) return

      // 使用nextTick确保DOM更新完成后再选中
      this.$nextTick(() => {
        // 设置选中的节点
        this.selectedKeys = [nodeKey]
        this.treeDataManager.setSelectedKeys(this.selectedKeys)

        // 设置路径数据到属性面板
        this.selectedPathData = { ...pathData }
        this.selectedPositionData = null // 清空位置数据

        // 强制触发Vue响应式更新
        this.$forceUpdate()

        // 设置当前选中的地图ID
        const [mapId] = nodeKey.split('_')
        this.currentSelectedMapId = mapId

        // 先清除所有路径选中状态，然后选中新路径
        this.pathSystemManager.clearAllSelections()
        this.pathSystemManager.selectPath(pathData.uuid)
      })
    },

    createObject(type, position, businessData = null) {
      // 验证position参数
      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {
        console.error('createObject: Invalid position parameter:', position)
        position = new THREE.Vector3(0, 0, 0)
      }

      let object
      const name = businessData?.name || `位置${Date.now()}`

      try {
        switch (type) {
          case 'person': {
            // 计算适合当前地图分辨率的缩放比例
            const scale = this.calculatePersonScale()
            const rotation = businessData?.rotation || 0
            console.log(`创建Person对象 ${name}: rotation=${rotation} 弧度 (${rotation * 180 / Math.PI} 度)`)
            object = makeThreeObjectNonReactive(new Person(name, name, position, scale, rotation))
            // 如果有业务数据，保存到对象中
            if (businessData) {
              // 设置uuid到Person对象中，用于userData
              object.uuid = businessData.uuid || null
              object.actionNo = businessData.actionNo || ''
              object.contentDetailZh = businessData.contentDetailZh || ''
              object.contentDetailEn = businessData.contentDetailEn || ''
              object.contentDetailJa = businessData.contentDetailJa || ''
              object.contentDetailKo = businessData.contentDetailKo || ''
              // 保存状态标记
              object.isSaved = businessData.isSaved || false
              // 更新mesh的userData以包含正确的uuid
              if (object.mesh && object.mesh.userData) {
                object.mesh.userData.objectUuid = object.uuid
              }
            }
            break
          }
          default:
            console.warn('createObject: Unknown object type:', type)
            return
        }

        if (!object || !object.mesh) {
          console.error('createObject: Failed to create object or mesh')
          return
        }

        this.scene.add(object.mesh)
        this.sceneObjects.push(object)

        // 如果有旋转角度，在添加到场景后再次确保设置正确
        if (businessData && businessData.rotation !== undefined && businessData.rotation !== 0) {
          console.log(`在添加到场景后重新确认旋转: ${object.name} -> ${businessData.rotation} 弧度`)
          object.setRotation(businessData.rotation)
        }

        // 性能优化：更新拖拽管理器的可拖拽对象缓存
        if (this.dragManager) {
          this.dragManager.updateDraggableObjectsCache(this.sceneObjects)
        }

        // 正确选中新创建的对象（这会取消之前对象的选中状态）
        this.selectObject(object)
      } catch (error) {
        console.error(`Error creating ${type} object:`, error)
      }
    },

    selectObject(object) {
      // 取消之前选中对象的高亮
      if (this.selectedObject && this.selectedObject.setSelected) {
        this.selectedObject.setSelected(false)
      }

      // 总是先清除所有路径选中状态，然后再选中新的路径（如果有）
      if (this.pathSystemManager) {
        this.pathSystemManager.clearAllSelections()
      }

      this.selectedObject = object

      // 如果选中的是路径对象，需要重新选中它
      if (object && object.type === 'Group' && object.userData && object.userData.objectType === 'path') {
        const pathUuid = object.userData.objectUuid
        if (pathUuid && this.pathSystemManager) {
          // 直接通过PathSystemManager选中路径
          this.pathSystemManager.selectPath(pathUuid)
        }
      } else {
        // 高亮当前选中对象（非路径对象）
        if (object && object.setSelected) {
          object.setSelected(true)
        }
      }

      // 根据对象类型设置相应的数据到属性面板
      if (object && object.userData && object.userData.objectType === 'path') {
        // 路径对象 - 通过路径系统管理器获取路径数据
        const pathUuid = object.userData.objectUuid
        const pathObject = this.pathSystemManager.getPath(pathUuid)
        if (pathObject) {
          this.selectedPathData = pathObject.getPathData()
          this.selectedPositionData = null
        }
      } else {
        // 位置对象或其他
        this.setPositionDataFromObject(object)
        this.selectedPathData = null
      }

      // 同步选中树形结构中对应的节点
      if (object) {
        this.syncTreeSelection(object)
      } else {
        // 取消选中时，清空树形结构的选中状态
        this.selectedKeys = []
        this.treeDataManager.setSelectedKeys([])
        // 清空属性面板
        this.selectedPositionData = null
        this.selectedPathData = null
      }

      // 强制更新Vue界面
      this.$forceUpdate()
    },

    // 同步选中树形结构中对应的节点
    syncTreeSelection(object) {
      const treeNode = this.treeDataManager.findTreeNodeByObject(object, this.currentMapId)

      if (treeNode) {
        // 选中对应的树节点
        this.selectedKeys = [treeNode.key]
        this.treeDataManager.setSelectedKeys(this.selectedKeys)
        console.log(`已同步选中树节点: ${treeNode.title}`)

        // 自动展开到对应的级别
        this.autoExpandTreeToNode(treeNode)
      }
    },

    // 自动展开树形结构到指定节点
    autoExpandTreeToNode(treeNode) {
      if (!treeNode) return

      const newExpandedKeys = [...this.expandedKeys]

      // 根据节点类型确定需要展开的父级节点
      if (treeNode.objectType === 'position' || treeNode.objectType === 'path') {
        // 这是位置或路径节点，需要展开地图节点和分类节点
        const mapId = treeNode.mapId.toString()

        // 确保地图节点展开
        if (!newExpandedKeys.includes(mapId)) {
          newExpandedKeys.push(mapId)
          console.log(`自动展开地图节点: ${mapId}`)
        }

        // 确保分类节点展开
        const categoryKey = treeNode.objectType === 'position'
          ? `${mapId}_positions`
          : `${mapId}_paths`

        if (!newExpandedKeys.includes(categoryKey)) {
          newExpandedKeys.push(categoryKey)
          console.log(`自动展开分类节点: ${categoryKey}`)
        }
      } else if (treeNode.objectType === 'category') {
        // 这是分类节点，只需要展开地图节点
        const mapId = treeNode.mapId.toString()

        if (!newExpandedKeys.includes(mapId)) {
          newExpandedKeys.push(mapId)
          console.log(`自动展开地图节点: ${mapId}`)
        }
      }

      // 更新展开状态
      if (newExpandedKeys.length > this.expandedKeys.length) {
        this.expandedKeys = newExpandedKeys
        this.treeDataManager.setExpandedKeys(newExpandedKeys)
        console.log('树形结构已自动展开到选中节点')
      }
    },

    // 从3D对象设置位置数据（统一属性面板）
    setPositionDataFromObject(object) {
      this.selectedPositionData = this.positionManager.setPositionDataFromObject(object)
    },

    // 更新位置点属性
    updatePositionProperty(property, value) {
      if (this.selectedPositionData) {
        // 对坐标属性进行边界限制
        if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
          const numValue = parseFloat(value) || 0
          let clampedValue = numValue

          // 根据属性类型应用相应的边界限制
          if (property === 'xcoordinate') {
            clampedValue = Math.max(this.coordinateBounds.minX, Math.min(this.coordinateBounds.maxX, numValue))
          } else if (property === 'ycoordinate') {
            clampedValue = Math.max(this.coordinateBounds.minY, Math.min(this.coordinateBounds.maxY, numValue))
          } else if (property === 'zcoordinate') {
            clampedValue = Math.max(this.coordinateBounds.minZ, Math.min(this.coordinateBounds.maxZ, numValue))
          }

          // 保留两位小数
          clampedValue = Math.round(clampedValue * 100) / 100

          // 如果值被限制了，显示提示并更新输入框
          if (clampedValue !== numValue) {
            message.info(`坐标值已限制在有效范围内: ${clampedValue}`)
            // 更新输入框显示的值
            this.selectedPositionData[property] = clampedValue.toString()
          }

          value = clampedValue
        }

        // 对yaw属性进行精度处理
        if (property === 'yaw') {
          const numValue = parseFloat(value) || 0
          // 保留两位小数并确保在0-360范围内
          let clampedValue = Math.round(numValue * 100) / 100
          if (clampedValue < 0) clampedValue += 360
          if (clampedValue >= 360) clampedValue -= 360

          value = clampedValue
          // 更新输入框显示的值
          this.selectedPositionData[property] = clampedValue.toString()
        }

        // 使用PositionManager更新属性
        this.positionManager.updatePositionProperty(this.selectedPositionData, property, value)

        // 同步更新树形数据中的objectData
        const updated = this.treeDataManager.updateTreeNodeObjectData(
          property,
          value,
          this.currentMapId,
          this.selectedKeys
        )

        if (updated) {
          this.treeData = this.treeDataManager.getTreeData()
        }

        // 更新3D场景中的对象
        this.positionManager.updateSceneObjectFromPositionData(
          this.selectedPositionData,
          this.selectedObject,
          property,
          value
        )

        // 如果是坐标属性发生变化，需要更新所有引用了该位置的路径
        if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
          this.updatePathsReferencingPosition(this.selectedPositionData.uuid)
        }
      }
    },

    // 清空所有引用指定位置的路径的位置引用
    clearPathReferencesToPosition(positionUuid) {
      if (!positionUuid || !this.currentMapId) {
        return
      }

      console.log(`位置 ${positionUuid} 被删除，开始清空相关路径的位置引用`)

      // 获取当前地图的所有路径
      const mapPaths = this.pathSystemManager.getMapPaths(this.currentMapId)

      // 查找引用了该位置的路径
      const affectedPaths = mapPaths.filter(path =>
        path.positionA === positionUuid || path.positionB === positionUuid
      )

      if (affectedPaths.length === 0) {
        console.log(`没有路径引用位置 ${positionUuid}`)
        return
      }

      console.log(`找到 ${affectedPaths.length} 个路径引用了位置 ${positionUuid}，开始清空引用`)

      // 清空每个受影响路径的位置引用
      affectedPaths.forEach(pathObject => {
        let updated = false

        // 如果该位置是路径的起点，清空positionA
        if (pathObject.positionA === positionUuid) {
          console.log(`清空路径 ${pathObject.name} 的位置A引用`)
          pathObject.positionA = null
          updated = true
        }

        // 如果该位置是路径的终点，清空positionB
        if (pathObject.positionB === positionUuid) {
          console.log(`清空路径 ${pathObject.name} 的位置B引用`)
          pathObject.positionB = null
          updated = true
        }

        if (updated) {
          console.log(`路径 ${pathObject.name} 的位置引用已清空`)
        }
      })

      console.log(`已清空 ${affectedPaths.length} 个路径的位置引用`)
    },

    // 更新所有引用了指定位置的路径
    updatePathsReferencingPosition(positionUuid) {
      if (!positionUuid || !this.currentMapId) {
        return
      }

      console.log(`位置 ${positionUuid} 坐标发生变化，开始更新相关路径`)

      // 获取当前地图的所有路径
      const mapPaths = this.pathSystemManager.getMapPaths(this.currentMapId)

      // 查找引用了该位置的路径
      const affectedPaths = mapPaths.filter(path =>
        path.positionA === positionUuid || path.positionB === positionUuid
      )

      if (affectedPaths.length === 0) {
        console.log(`没有路径引用位置 ${positionUuid}`)
        return
      }

      console.log(`找到 ${affectedPaths.length} 个路径引用了位置 ${positionUuid}`)

      // 获取位置的最新坐标
      const position = this.currentMapPositions.find(pos => pos.uuid === positionUuid)
      if (!position) {
        console.warn(`未找到位置信息，UUID: ${positionUuid}`)
        return
      }

      const newPoint = {
        x: parseFloat(position.xcoordinate) || 0,
        y: parseFloat(position.ycoordinate) || 0, // 允许Y坐标变化
        z: 0.1 // 路径固定在XY平面上，稍微抬高避免Z-fighting
      }

      // 更新每个受影响的路径
      affectedPaths.forEach(pathObject => {
        let updated = false

        // 如果该位置是路径的起点
        if (pathObject.positionA === positionUuid) {
          console.log(`更新路径 ${pathObject.name} 的起点坐标`)
          pathObject.startPoint = { ...newPoint }
          if (pathObject.startControlPoint) {
            pathObject.startControlPoint.position.set(newPoint.x, newPoint.y, newPoint.z)
          }
          updated = true
        }

        // 如果该位置是路径的终点
        if (pathObject.positionB === positionUuid) {
          console.log(`更新路径 ${pathObject.name} 的终点坐标`)
          pathObject.endPoint = { ...newPoint }
          if (pathObject.endControlPoint) {
            pathObject.endControlPoint.position.set(newPoint.x, newPoint.y, newPoint.z)
          }
          updated = true
        }

        // 如果路径被更新了，重新创建路径以更新线条
        if (updated) {
          pathObject.createPath()
          console.log(`路径 ${pathObject.name} 已更新`)
        }
      })

      console.log(`位置 ${positionUuid} 相关的 ${affectedPaths.length} 个路径已更新完成`)
    },

    // 处理位置A变更
    handlePositionAChange(value) {
      console.log(`位置A变化: ${value}`)
      this.updatePathProperty('positionA', value)

      // 根据选择的位置A更新路径起点坐标
      this.updatePathPointFromPosition(value, 'startPoint')
    },

    // 处理位置B变更
    handlePositionBChange(value) {
      console.log(`位置B变化: ${value}`)
      this.updatePathProperty('positionB', value)

      // 根据选择的位置B更新路径终点坐标
      this.updatePathPointFromPosition(value, 'endPoint')
    },

    // 根据位置UUID更新路径的起点或终点坐标
    updatePathPointFromPosition(positionUuid, pointType) {
      if (!positionUuid || !this.selectedPathData) {
        return
      }

      // 查找对应的位置信息
      const position = this.currentMapPositions.find(pos => pos.uuid === positionUuid)
      if (!position) {
        console.warn(`未找到位置信息，UUID: ${positionUuid}`)
        return
      }

      // 获取路径对象
      const pathObject = this.pathSystemManager.getPath(this.selectedPathData.uuid)
      if (!pathObject) {
        console.warn(`未找到路径对象，UUID: ${this.selectedPathData.uuid}`)
        return
      }

      // 构建新的坐标点
      const newPoint = {
        x: parseFloat(position.xcoordinate) || 0,
        y: parseFloat(position.ycoordinate) || 0, // 允许Y坐标变化
        z: 0.1 // 路径固定在XY平面上，稍微抬高避免Z-fighting
      }

      console.log(`更新路径${pointType}坐标:`, newPoint, `来自位置: ${position.name}`)

      // 更新路径对象的坐标点
      if (pointType === 'startPoint') {
        pathObject.startPoint = newPoint
        if (pathObject.startControlPoint) {
          pathObject.startControlPoint.position.set(newPoint.x, newPoint.y, newPoint.z)
        }
      } else if (pointType === 'endPoint') {
        pathObject.endPoint = newPoint
        if (pathObject.endControlPoint) {
          pathObject.endControlPoint.position.set(newPoint.x, newPoint.y, newPoint.z)
        }
      }

      // 重新创建路径以更新线条
      pathObject.createPath()
    },

    // 处理方向变化
    handleDirectionChange(value) {
      console.log(`方向变化: ${value}`)
      this.updatePathProperty('direction', value)
    },

    // 更新路径属性
    updatePathProperty(property, value) {
      if (!this.selectedPathData) return

      // 处理普通属性
      this.selectedPathData[property] = value
      // 强制触发响应式更新
      this.selectedPathData = { ...this.selectedPathData }

      // 同步更新树形数据中的objectData
      const updated = this.treeDataManager.updateTreeNodeObjectData(
        property,
        value,
        this.currentMapId,
        this.selectedKeys
      )

      if (updated) {
        this.treeData = this.treeDataManager.getTreeData()
      }

      // 更新路径系统中的路径对象
      const pathObject = this.pathSystemManager.getPath(this.selectedPathData.uuid)
      if (pathObject) {
        pathObject.updateProperty(property, value)
      }
    },

    // 鼠标滚轮事件处理
    onCanvasWheel(event) {
      // 只有在选中对象且按住Shift键时才处理滚轮事件
      if (!this.selectedObject || !this.selectedPositionData || !this.isShiftPressed) {
        return // 让OrbitControls处理缩放
      }

      // 完全阻止事件传播，防止OrbitControls处理
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()

      // 计算旋转增量（每次滚动15度）
      const rotationDelta = event.deltaY > 0 ? 15 : -15

      // 使用PositionManager处理旋转
      const newYaw = this.positionManager.handleRotation(this.selectedPositionData, rotationDelta)

      if (newYaw !== null) {
        // 更新位置数据
        this.selectedPositionData.yaw = newYaw.toString()
        this.updatePositionProperty('yaw', newYaw.toString())

        // 显示临时提示
        this.showRotationHint(newYaw, 'Shift+滚轮调整')
      }

      return false // 确保事件不会继续传播
    },

    // 键盘状态监听（用于跟踪Shift键状态）
    onKeyStateChange(event) {
      this.isShiftPressed = event.shiftKey

      // 当Shift键状态改变时，动态控制OrbitControls的缩放功能
      if (this.controls && this.selectedObject && this.selectedPositionData) {
        if (this.isShiftPressed) {
          // 按下Shift时，如果有选中对象，禁用缩放
          this.controls.enableZoom = false
        } else {
          // 释放Shift时，重新启用缩放
          this.controls.enableZoom = true
        }
      }
    },

    // 键盘事件处理
    onKeyDown(event) {
      // 只有在选中对象时才处理键盘事件
      if (!this.selectedObject || !this.selectedPositionData) {
        return
      }

      // 检查是否在输入框中，如果是则不处理
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return
      }

      let rotationDelta = 0

      switch (event.key) {
        case 'q':
        case 'Q':
          rotationDelta = -15 // 逆时针15度
          break
        case 'e':
        case 'E':
          rotationDelta = 15 // 顺时针15度
          break
        case 'ArrowLeft':
          if (event.ctrlKey) {
            rotationDelta = -15
          }
          break
        case 'ArrowRight':
          if (event.ctrlKey) {
            rotationDelta = 15
          }
          break
        default:
          return // 不处理其他按键
      }

      if (rotationDelta !== 0) {
        event.preventDefault()

        // 使用PositionManager处理旋转
        const newYaw = this.positionManager.handleRotation(this.selectedPositionData, rotationDelta)

        if (newYaw !== null) {
          // 更新位置数据
          this.selectedPositionData.yaw = newYaw.toString()
          this.updatePositionProperty('yaw', newYaw.toString())

          // 显示提示
          this.showRotationHint(newYaw, `按键: ${event.key.toUpperCase()}`)
        }
      }
    },

    // 显示旋转提示（增强版）
    showRotationHint(yaw, method = 'Shift+滚轮调整') {
      // 创建或更新提示元素
      let hint = document.getElementById('rotation-hint')
      if (!hint) {
        hint = document.createElement('div')
        hint.id = 'rotation-hint'
        hint.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 10px 15px;
          border-radius: 5px;
          font-size: 14px;
          z-index: 1000;
          pointer-events: none;
          transition: opacity 0.3s;
        `
        document.body.appendChild(hint)
      }

      hint.innerHTML = `
        <div>朝向: ${Math.round(yaw)}°</div>
        <div style="font-size: 12px; opacity: 0.8; margin-top: 2px;">${method}</div>
      `
      hint.style.opacity = '1'

      // 2秒后淡出
      clearTimeout(this.hintTimeout)
      this.hintTimeout = setTimeout(() => {
        if (hint) {
          hint.style.opacity = '0'
          setTimeout(() => {
            if (hint && hint.parentNode) {
              hint.parentNode.removeChild(hint)
            }
          }, 300)
        }
      }, 2000)
    },



    // 持续更新渲染 - 性能优化版本
    animate() {
      requestAnimationFrame(this.animate)

      // 更新性能监控
      performanceMonitor.update(this.renderer)

      // 自动渲染优化
      renderOptimizer.autoOptimize(
        performanceMonitor.getStats(),
        this.pgmRenderer,
        this.pcdRenderer
      )

      // 更新相机控制器
      this.controls.update()

      // 更新PCD LOD（如果存在）
      if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        this.pcdRenderer.updateLOD(this.camera)
      }

      // 更新所有场景对象
      this.sceneObjects.forEach(obj => {
        if (obj.update) {
          obj.update()
        }
      })

      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    },

    // 初始化性能监控
    initPerformanceMonitoring() {
      // 启动性能监控
      performanceMonitor.start()

      // 设置性能阈值
      performanceMonitor.setThresholds({
        lowFPS: 25,
        highMemory: 300 * 1024 * 1024, // 300MB
        maxPoints: 150000
      })

      // 添加性能回调
      performanceMonitor.addCallback((stats) => {
        this.performanceStats = {
          fps: stats.fps,
          memoryMB: stats.memoryMB,
          points: stats.points
        }

        // 自动优化建议
        if (stats.fps < 20 && this.pcdRenderer && this.pcdRenderer.hasPCD()) {
          console.warn('FPS过低，建议减少点云密度')
          // 可以在这里自动切换到更低的LOD
        }
      })

      console.log('性能监控已初始化')
    },

    // 切换性能面板显示
    togglePerformancePanel() {
      this.showPerformancePanel = !this.showPerformancePanel
    },

    // 优化渲染性能
    optimizePerformance(advice = null) {
      // 如果没有传入advice，则获取当前的性能建议
      if (!advice) {
        const stats = performanceMonitor.getStats()
        advice = renderOptimizer.getOptimizationAdvice(stats)
        console.log('性能统计:', stats)
        console.log('优化建议:', advice)

        // 应用自动优化
        renderOptimizer.autoOptimize(stats, this.pgmRenderer, this.pcdRenderer)
      }

      let optimized = false

      // 应用优化建议
      if (advice.reducePCDDensity && this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        this.pcdRenderer.setLOD(Math.min(this.pcdRenderer.currentLOD + 1, 3))
        optimized = true
        console.log('已降低PCD密度')
      }

      if (advice.reducePGMQuality && this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        // 可以在这里添加PGM质量降低的逻辑
        console.log('建议降低PGM质量')
      }

      if (advice.enableFrustumCulling && this.renderer) {
        // 启用视锥体剔除
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5))
        optimized = true
        console.log('已启用视锥体剔除优化')
      }

      if (advice.reduceRenderDistance && this.renderer && this.camera) {
        // 减少渲染距离
        this.camera.far = Math.max(this.camera.far * 0.8, 100)
        this.camera.updateProjectionMatrix()
        optimized = true
        console.log('已减少渲染距离')
      }

      // 根据建议执行特定优化
      if (Array.isArray(advice)) {
        advice.forEach(item => {
          switch (item.action) {
            case 'reduce_quality':
              renderOptimizer.applyPreset('performance', this.pgmRenderer, this.pcdRenderer)
              message.info('已切换到性能优先模式')
              break
            case 'clear_cache':
              if (this.pgmRenderer) {
                this.pgmRenderer.clearCache()
              }
              if (this.pcdRenderer) {
                this.pcdRenderer.clearCache()
              }
              message.info('已清理缓存以释放内存')
              break
            case 'enable_lod':
              if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
                // 强制切换到较低的LOD
                const lodLevels = this.pcdRenderer.lodLevels.length
                if (lodLevels > 1) {
                  this.pcdRenderer.switchToLOD(Math.min(2, lodLevels - 1))
                  message.info('已启用LOD优化')
                }
              }
              break
          }
        })
      }

      if (optimized || (Array.isArray(advice) && advice.length > 0)) {
        message.success('性能优化已应用')
      } else {
        message.success('当前性能良好，无需优化')
      }
    },



    // 获取渲染优化状态
    getRenderOptimizerStatus() {
      return renderOptimizer.getCurrentSettings()
    },

    // 清理PCD渲染
    clearPCDRendering() {
      if (this.pcdRenderer) {
        console.log('清理PCD渲染...')
        console.log('PCD渲染状态:', this.isPCDRendered)
        console.log('PCD网格存在:', this.pcdRenderer.hasPCD())

        this.pcdRenderer.dispose()
        this.isPCDRendered = false
        console.log('PCD渲染已清理')
      }
    },

    // 清理PGM渲染
    clearPGMRendering() {
      if (this.pgmRenderer) {
        console.log('清理PGM渲染...')
        this.pgmRenderer.dispose()
        console.log('PGM渲染已清理')
      }
    },

    // 清理所有地图渲染
    clearAllMapRendering() {
      console.log('开始清理所有地图渲染...')
      this.clearPCDRendering()
      this.clearPGMRendering()
      console.log('所有地图渲染清理完成')
    },



    // 拖拽对象位置更新回调
    onDraggedObjectPositionUpdate(objectType, objectUuid, newPosition) {
      // 找到对应的对象
      const object = this.sceneObjects.find(obj => obj.uuid === objectUuid && obj.type === objectType)
      if (!object) {
        return
      }

      // 限制位置在边界范围内（优先PGM，其次PCD）
      let clampedPosition = newPosition
      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        clampedPosition = this.positionManager.validateAndClampPosition(newPosition, this.pgmRenderer)

        // 如果位置被限制了，显示提示
        if (clampedPosition.x !== newPosition.x || clampedPosition.y !== newPosition.y) {
          console.log('拖拽位置被限制在PGM范围内')
        }
      } else if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        // PCD边界限制（XY平面拖拽）
        const clampedPoint = this.pcdRenderer.clampPointToPCDBounds(newPosition.x, newPosition.y)
        clampedPosition = {
          x: clampedPoint.x,
          y: clampedPoint.y,
          z: newPosition.z
        }

        // 如果位置被限制了，显示提示
        if (clampedPosition.x !== newPosition.x || clampedPosition.y !== newPosition.y) {
          console.log('拖拽位置被限制在PCD范围内')
        }
      }

      // 更新对象的内部位置
      object.position.copy(clampedPosition)

      // 根据对象类型进行特殊处理
      switch (objectType) {
        case 'person':
          // 人物对象：确保mesh位置同步
          if (object.mesh) {
            object.mesh.position.copy(clampedPosition)
          }
          break
      }

      // 同步更新位置数据到树形结构
      this.updatePositionDataFromObject(object)

      // 如果当前选中的是这个对象，触发界面更新
      if (this.selectedObject && this.selectedObject.uuid === objectUuid) {
        // 更新选中的位置数据，保留两位小数
        if (this.selectedPositionData) {
          this.selectedPositionData.xcoordinate = (Math.round(clampedPosition.x * 100) / 100).toString()
          this.selectedPositionData.ycoordinate = (Math.round(clampedPosition.y * 100) / 100).toString()
          this.selectedPositionData.zcoordinate = (Math.round(clampedPosition.z * 100) / 100).toString()
        }
        // 强制Vue更新界面
        this.$forceUpdate()
      }

      // 如果是位置元素被拖拽，需要更新所有引用了该位置的路径
      if (objectType === 'person' && objectUuid) {
        this.updatePathsReferencingPosition(objectUuid)
      }
    },


    // 导入对象
    importObjects(objects) {
      objects.forEach(objData => {
        try {
          this.createObjectFromData(objData)
        } catch (error) {
          console.error(`导入对象失败 (uuid: ${objData.uuid}):`, error)
        }
      })
    },

    // 计算人物模型的缩放比例（根据地图分辨率）
    calculatePersonScale() {
      // 基准分辨率：0.05 米/像素（对应缩放比例 1.0）
      const baseResolution = 0.05

      // 获取当前地图的分辨率
      let currentResolution = baseResolution

      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        const bounds = this.pgmRenderer.getPGMBounds()
        if (bounds && bounds.resolution) {
          currentResolution = bounds.resolution
        }
      } else if (this.currentMapData && this.currentMapData.resolution) {
        // 如果没有渲染PGM但有地图数据，尝试从地图数据获取分辨率
        currentResolution = this.currentMapData.resolution
      }

      // 计算缩放比例：分辨率越小（越精细），人物应该越大
      const scale = baseResolution / currentResolution

      // 限制缩放范围，避免过大或过小
      const clampedScale = Math.max(0.5, Math.min(3.0, scale))

      console.log(`人物缩放计算 - 当前分辨率: ${currentResolution}, 基准分辨率: ${baseResolution}, 缩放比例: ${clampedScale}`)

      return clampedScale
    },

    // 更新现有位置元素的缩放比例
    updateExistingPersonScale() {
      const scale = this.calculatePersonScale()

      this.sceneObjects.forEach(obj => {
        if (obj.type === 'person' && obj.mesh) {
          // 移除旧的网格
          this.scene.remove(obj.mesh)

          // 更新缩放比例
          obj.scale = scale

          // 重新创建网格
          obj.createMesh()

          // 重新添加到场景
          this.scene.add(obj.mesh)

          // 如果是选中的对象，保持选中状态
          if (obj === this.selectedObject) {
            obj.setSelected(true)
          }
        }
      })

      console.log(`已更新 ${this.sceneObjects.filter(obj => obj.type === 'person').length} 个位置元素的缩放比例为: ${scale}`)
    },

    // 从数据创建对象
    createObjectFromData(objData) {
      // 创建三维向量的标准方法，表示坐标系原点位置的向量对象
      const position = new THREE.Vector3(objData.position.x, objData.position.y, objData.position.z)

      let object
      switch (objData.type) {
        case 'person': {
          // 计算适合当前地图分辨率的缩放比例
          const scale = this.calculatePersonScale()
          const rotation = objData.rotation || 0
          console.log(`创建Person对象 ${objData.name}: rotation=${rotation} 弧度 (${rotation * 180 / Math.PI} 度)`)
          object = makeThreeObjectNonReactive(new Person(objData.name, objData.name, position, scale, rotation))
          // 设置uuid到Person对象中，用于userData
          object.uuid = objData.uuid || null
          // 保存业务信息到3D对象中
          object.actionNo = objData.actionNo || ''
          object.contentDetailZh = objData.contentDetailZh || ''
          object.contentDetailEn = objData.contentDetailEn || ''
          object.contentDetailJa = objData.contentDetailJa || ''
          object.contentDetailKo = objData.contentDetailKo || ''
          // 保存状态标记
          object.isSaved = objData.isSaved || false
          break
        }
        default:
          console.warn('未知的对象类型:', objData.type)
          return
      }

      if (object && object.mesh) {
        this.scene.add(object.mesh)
        this.sceneObjects.push(object)

        // 如果有旋转角度，在添加到场景后再次确保设置正确
        if (objData.rotation !== undefined && objData.rotation !== 0) {
          console.log(`在添加到场景后重新设置旋转: ${objData.name} -> ${objData.rotation} 弧度`)
          object.setRotation(objData.rotation)
        }

        // 性能优化：更新拖拽管理器的可拖拽对象缓存
        if (this.dragManager) {
          this.dragManager.updateDraggableObjectsCache(this.sceneObjects)
        }
      }
    },

    // 清空场景（不显示确认对话框）
    clearSceneWithoutConfirm() {

      // 清理所有路径系统中的路径
      if (this.pathSystemManager) {

        // 获取所有路径并逐个清理
        const allPaths = Array.from(this.pathSystemManager.paths.values())

        allPaths.forEach(path => {
          if (path && path.group) {
            // 设置为不可见，立即停止渲染
            path.group.visible = false

            // 清空group的所有子对象
            const childrenToRemove = [...path.group.children]
            childrenToRemove.forEach(child => {
              path.group.remove(child)
              if (child.geometry) child.geometry.dispose()
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach(mat => mat.dispose())
                } else {
                  child.material.dispose()
                }
              }
            })

            // 从场景中移除路径的3D对象
            this.scene.remove(path.group)
            console.log(`已清理路径: ${path.name}`)
          }
        })

        // 清空路径管理器中的所有路径
        this.pathSystemManager.paths.clear()
        this.pathSystemManager.selectedPath = null
        this.pathSystemManager.editingPath = null
      }

      // 清理常规场景对象
      this.sceneObjects = SceneSetup.clearScene(this.scene, this.sceneObjects)
      this.selectedObject = null

      // 清理所有地图渲染（PCD和PGM）
      this.clearAllMapRendering()

      // 清空选中的位置和路径数据
      this.selectedPositionData = null
      this.selectedPathData = null

      // 重置拖拽管理器边界
      if (this.dragManager) {
        this.dragManager.resetBounds()
      }

      // 清理路径交互管理器中的选中状态
      if (this.pathInteractionManager) {
        this.pathInteractionManager.clearSelection()
      }

      // 清理树形数据的选中状态
      this.selectedKeys = []
      this.treeDataManager.setSelectedKeys([])

      // 恢复默认地板和网格（如果PGM被清理了）
      SceneSetup.showDefaultFloor(this.scene)
    },

  }
}

</script>

<style scoped>
.map3d-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

/* 第一列：地图列表面板 */
.map-list-panel {
  width: 12%;
  /* 占12%宽度 */
  background: #fafafa;
  border-right: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.map-list-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 5px;
}

/* 第二列：3D容器包装器 */
.three-container-wrapper {
  width: 72%;
  /* 占72%宽度 */
  display: flex;
  flex-direction: column;
}

/* 操作区 */
.map-operations {
  background: #fff;
  border-bottom: 1px solid #ddd;
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 拖动元素容器样式 */
.drag-items-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 拖动位置元素样式 */
.drag-position-item {
  background: #f0f8ff;
  border: 2px dashed #1890ff;
  border-radius: 6px;
  padding: 6px 30px;
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.drag-position-item:hover {
  background: #e6f7ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.drag-position-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

/* 第二列：3D容器 */
.three-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 第三列：控制面板 */
.control-panel {
  width: 16%;
  /* 占16%宽度 */
  background: #f5f5f5;
  border-left: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.object-list {
  margin-bottom: 30px;
}

.object-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.object-item:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.object-item.selected {
  background: #e3f2fd;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.delete-btn:hover {
  background: #c82333;
}

.object-properties {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.empty-state {
  height: 100%;
  /* min-height: 300px; */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.position-actions {
  margin-bottom: 15px;
  padding-bottom: 15px;
  /* border-bottom: 1px solid #eee; */
}

/* 属性行布局优化 */
.property-group {
  margin-bottom: 12px;
}

.property-group.inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.property-group.block {
  display: block;
}

.property-group.inline label {
  width: 66px;
  margin: 0;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.property-group.inline .ant-input,
.property-group.inline .ant-input-number,
.property-group.inline .ant-select {
  width: 140px;
}

.property-group.block .ant-input,
.property-group.block .ant-textarea {
  width: 100%;
}

.property-group input,
.property-group .ant-input-number {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}


.edit-btn,
.add-btn,
.move-btn,
.stop-btn,
.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 5px 5px 5px 0;
  transition: background-color 0.2s ease;
}

.edit-btn {
  background: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #138496;
}

.add-btn {
  background: #28a745;
  color: white;
}

.add-btn:hover {
  background: #218838;
}

.move-btn {
  background: #007bff;
  color: white;
}

.move-btn:hover {
  background: #0056b3;
}



.move-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.stop-btn {
  background: #dc3545;
  color: white;
}

.stop-btn:hover {
  background: #c82333;
}

.action-btn {
  background: #6c757d;
  color: white;
  width: 100%;
  margin-bottom: 10px;
}

.action-btn:hover {
  background: #545b62;
}

.action-btn.danger {
  background: #dc3545;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn.export {
  background: #28a745;
}

.action-btn.export:hover {
  background: #218838;
}

.action-btn.import {
  background: #17a2b8;
}

.action-btn.import:hover {
  background: #138496;
}

/* 拖动直线路径元素样式 */
.drag-straight-path-item {
  background: #e6f7ff;
  border: 2px dashed #1890ff;
  border-radius: 6px;
  padding: 6px 30px;
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.drag-straight-path-item:hover {
  background: #bae7ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.drag-straight-path-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

/* 拖动贝塞尔曲线路径元素样式 */
.drag-bezier-path-item {
  background: #f6ffed;
  border: 2px dashed #52c41a;
  border-radius: 6px;
  padding: 6px 30px;
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #52c41a;
  font-weight: 500;
}

.drag-bezier-path-item:hover {
  background: #f0f9e8;
  border-color: #73d13d;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
}

.drag-bezier-path-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

:deep(.ant-tabs-tab + .ant-tabs-tab) {
  margin: 0 0 0 16px !important;
}
</style>