/**
 * 地图配置模块
 * 包含地图数据和相关配置
 */

/**
 * 地图配置类
 */
export class MapConfig {

  /**
   * 将位置点数据转换为3D场景对象格式
   */
  static convertPositionDataToSceneObjects(positionData) {
    return positionData.map(pos => ({
      name: pos.name,
      type: 'person', // 默认类型为person
      position: {
        x: parseFloat(pos.xcoordinate) || 0,
        y: parseFloat(pos.ycoordinate) || 0,
        z: parseFloat(pos.zcoordinate) || 0
      },
      rotation: (parseFloat(pos.yaw) || 0) * Math.PI / 180, // 将角度转换为弧度
      actionNo: pos.actionNo, // 保留动作编号
      contentDetailZh: pos.contentDetailZh || '', // 中文内容
      contentDetailEn: pos.contentDetailEn || '', // 英文内容
      contentDetailJa: pos.contentDetailJa || '', // 日文内容
      contentDetailKo: pos.contentDetailKo || '', // 韩文内容
      uuid: pos.uuid, // 保存UUID
      isSaved: true // 从服务器加载的数据标记为已保存
    }))
  }

  /**
   * 收集当前地图的所有位置点信息
   */
  static collectMapPositions(treeData, currentMapId, sceneObjects) {
    const positions = []

    // 从树形数据中获取位置点信息，确保类型匹配（三级结构）
    const mapIdStr = currentMapId.toString()
    const mapNode = treeData.find(node => node.key === mapIdStr || node.key === currentMapId)
    if (mapNode && mapNode.children) {
      // 查找位置分类节点
      const positionsNode = mapNode.children.find(child =>
        child.categoryType === 'positions' && child.objectType === 'category'
      )

      if (positionsNode && positionsNode.children) {
        positionsNode.children.forEach(child => {
          if (child.isLeaf && child.objectData && child.objectType === 'position') {
            const posData = child.objectData

            // 查找对应的3D对象以获取最新的坐标和旋转信息
            const sceneObject = sceneObjects.find(obj => obj.uuid === posData.uuid)

            // 只有在3D场景中实际存在的位置才会被收集（避免收集已删除的位置）
            if (sceneObject) {
              const position = {
                uuid: posData.uuid || null,
                name: posData.name || '',
                xcoordinate: (Math.round(sceneObject.position.x * 100) / 100).toString(), // 保留两位小数
                ycoordinate: (Math.round(sceneObject.position.y * 100) / 100).toString(), // 保留两位小数
                zcoordinate: (Math.round(sceneObject.position.z * 100) / 100).toString(), // 保留两位小数
                yaw: sceneObject.getRotationDegrees ?
                  (Math.round(sceneObject.getRotationDegrees() * 100) / 100).toString() : (posData.yaw || '0'), // 保留两位小数
                actionNo: posData.actionNo || '',
                contentDetailZh: posData.contentDetailZh || '', // 中文内容
                contentDetailEn: posData.contentDetailEn || '', // 英文内容
                contentDetailJa: posData.contentDetailJa || '', // 日文内容
                contentDetailKo: posData.contentDetailKo || '', // 韩文内容
                mapId: currentMapId
              }

              positions.push(position)
            } else {
              console.warn(`位置 ${posData.name} (uuid: ${posData.uuid}) 在3D场景中不存在，跳过收集`)
            }
          }
        })
      }
    }

    return positions
  }

  /**
   * 收集当前地图的所有路径信息
   */
  static collectMapPaths(treeData, currentMapId, pathSystemManager) {
    const paths = []

    // 从树形数据中获取路径信息，确保类型匹配（三级结构）
    const mapIdStr = currentMapId.toString()
    const mapNode = treeData.find(node => node.key === mapIdStr || node.key === currentMapId)
    if (mapNode && mapNode.children) {
      // 查找路径分类节点
      const pathsNode = mapNode.children.find(child =>
        child.categoryType === 'paths' && child.objectType === 'category'
      )

      if (pathsNode && pathsNode.children) {
        pathsNode.children.forEach(child => {
          if (child.isLeaf && child.objectData && child.objectType === 'path') {
            const pathData = child.objectData

            // 查找对应的路径对象以获取最新的路径信息
            const pathObject = pathSystemManager ? pathSystemManager.getPath(pathData.uuid) : null

            if (pathObject) {
              // 从路径对象获取最新数据
              const latestPathData = pathObject.getPathData()

              const path = {
                uuid: latestPathData.uuid || null,
                name: latestPathData.name || '',
                type: latestPathData.type || 'straight',
                positionA: latestPathData.positionA || null,
                positionB: latestPathData.positionB || null,
                direction: latestPathData.direction || 'A_to_B',
                linearVelocity: latestPathData.linearVelocity || 1.0,
                angularVelocity: latestPathData.angularVelocity || 1.0,
                description: latestPathData.description || '',
                centralPoint: latestPathData.centralPoint || [], // 中间控制点坐标（按方向排序）
                mapId: currentMapId
              }

              paths.push(path)
            } else {
              console.warn(`路径 ${pathData.name} (uuid: ${pathData.uuid}) 在路径系统中不存在，跳过收集`)
            }
          }
        })
      }
    }

    return paths
  }
}
