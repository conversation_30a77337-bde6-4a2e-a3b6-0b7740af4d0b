# 位置元素与路径线同步功能测试指南

## 功能描述
当位置元素的坐标发生变化时，所有引用了该位置的路径线会自动更新其端点坐标，保持路径线与位置元素的同步。

## 实现的功能
1. **属性面板修改坐标时同步更新路径**：当在属性面板中修改位置元素的X、Y、Z坐标时，所有引用该位置的路径会自动更新
2. **拖拽位置元素时同步更新路径**：当拖拽位置元素改变其坐标时，相关路径也会实时更新

## 测试步骤

### 准备工作
1. 打开应用：http://localhost:8080/
2. 选择或创建一个地图
3. 在地图上创建至少2个位置元素（通过拖拽"位置"按钮到地图上）
4. 创建一条路径线（直线或曲线），并将其位置A和位置B分别设置为刚创建的两个位置元素

### 测试场景1：通过属性面板修改坐标
1. 选中其中一个位置元素
2. 在右侧属性面板中修改该位置的X坐标或Z坐标
3. **预期结果**：
   - 位置元素在3D场景中移动到新位置
   - 所有引用该位置的路径线的对应端点也会移动到新位置
   - 路径线保持连接状态

### 测试场景2：通过拖拽修改位置
1. 直接在3D场景中拖拽位置元素到新位置
2. **预期结果**：
   - 位置元素移动到新位置
   - 属性面板中的坐标值实时更新
   - 所有引用该位置的路径线的对应端点也会跟随移动
   - 路径线保持连接状态

### 测试场景3：多路径引用同一位置
1. 创建3个位置元素：A、B、C
2. 创建两条路径：
   - 路径1：A -> B
   - 路径2：A -> C
3. 移动位置A（通过属性面板或拖拽）
4. **预期结果**：
   - 位置A移动到新位置
   - 路径1和路径2的起点都会跟随移动到新位置
   - 两条路径都保持正确的连接状态

## 技术实现要点

### 核心方法
- `updatePositionProperty()`: 处理属性面板的坐标修改
- `updatePathsReferencingPosition()`: 更新所有引用指定位置的路径
- `onDraggedObjectPositionUpdate()`: 处理拖拽位置更新

### 关键逻辑
1. 当位置坐标发生变化时，系统会：
   - 查找当前地图中所有的路径对象
   - 筛选出引用了该位置的路径（positionA或positionB等于该位置的UUID）
   - 更新这些路径的起点或终点坐标
   - 重新创建路径的3D表示

### 日志输出
在浏览器控制台中可以看到相关的调试信息：
- `位置 {uuid} 坐标发生变化，开始更新相关路径`
- `找到 {count} 个路径引用了位置 {uuid}`
- `更新路径 {name} 的起点/终点坐标`
- `路径 {name} 已更新`

## 注意事项
1. 只有当位置的X、Y、Z坐标发生变化时才会触发路径更新
2. 路径更新是实时的，无需手动刷新
3. 支持直线路径和贝塞尔曲线路径
4. 拖拽时会受到地图边界限制（如果有PGM或PCD文件）

## 故障排除
如果路径没有正确更新，请检查：
1. 路径的positionA/positionB是否正确设置为位置元素的UUID
2. 浏览器控制台是否有错误信息
3. 位置元素是否在当前地图中
4. 路径对象是否正确添加到PathSystemManager中
