/**
 * 位置管理模块
 * 处理位置点的创建、更新、删除等操作
 */

import * as THREE from 'three'
import { generateUUID } from './common.js'

/**
 * 位置管理器类
 */
export class PositionManager {
  constructor() {
    this.positionCounters = {}
  }

  /**
   * 获取当前地图的位置计数
   */
  getPositionCountForMap(mapId) {
    if (!this.positionCounters[mapId]) {
      this.positionCounters[mapId] = 1
    }
    return this.positionCounters[mapId]++
  }

  /**
   * 设置位置计数器
   */
  setPositionCounter(mapId, count) {
    this.positionCounters[mapId] = count
  }

  /**
   * 创建位置数据
   */
  createPositionData(targetMapId, position, existingPositions = []) {
    // 生成不重复的位置名称
    const name = this.generateUniquePositionName(targetMapId, existingPositions)
    const uuid = generateUUID() // 生成严谨的UUID作为唯一标识符

    // 创建位置数据
    const positionData = {
      name: name,
      xcoordinate: position.x.toString(),
      ycoordinate: position.y.toString(),
      zcoordinate: position.z.toString(),
      yaw: '0',
      actionNo: '',
      contentDetailZh: '', // 中文内容
      contentDetailEn: '', // 英文内容
      contentDetailJa: '', // 日文内容
      contentDetailKo: '', // 韩文内容
      uuid: uuid, // 使用UUID作为唯一标识符
      isSaved: false // 标记为新建，未保存到服务器
    }

    return {
      positionData,
      objectData: {
        name: name,
        actionNo: positionData.actionNo,
        contentDetailZh: positionData.contentDetailZh,
        contentDetailEn: positionData.contentDetailEn,
        contentDetailJa: positionData.contentDetailJa,
        contentDetailKo: positionData.contentDetailKo,
        rotation: 0,
        uuid: uuid, // 传递UUID
        isSaved: false // 标记为新建
      }
    }
  }

  /**
   * 生成不重复的位置名称
   */
  generateUniquePositionName(targetMapId, existingPositions = []) {
    // 获取所有已存在的位置名称
    const existingNames = new Set(existingPositions.map(pos => pos.name))

    // 从1开始尝试生成名称
    let counter = 1
    let name = `位置${counter}`

    // 如果名称已存在，递增计数器直到找到不重复的名称
    while (existingNames.has(name)) {
      counter++
      name = `位置${counter}`
    }

    // 更新位置计数器，确保下次从更大的数字开始
    this.setPositionCounter(targetMapId, counter + 1)

    return name
  }

  /**
   * 更新位置属性
   */
  updatePositionProperty(selectedPositionData, property, value) {
    if (!selectedPositionData) return null

    // 对于数字类型的属性，确保转换为字符串存储
    if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
      selectedPositionData[property] = String(value || 0)
    } else {
      selectedPositionData[property] = value
    }

    return selectedPositionData
  }

  /**
   * 从3D对象设置位置数据
   */
  setPositionDataFromObject(object) {
    if (!object) {
      return null
    }

    // 将3D对象数据转换为位置数据格式
    return {
      name: object.name || '未命名位置',
      xcoordinate: (object.position?.x || 0).toString(),
      ycoordinate: (object.position?.y || 0).toString(),
      zcoordinate: (object.position?.z || 0).toString(),
      yaw: object.getRotationDegrees ? (Math.round(object.getRotationDegrees() * 100) / 100).toString() : '0',
      actionNo: object.actionNo || '',
      contentDetailZh: object.contentDetailZh || '',
      contentDetailEn: object.contentDetailEn || '',
      contentDetailJa: object.contentDetailJa || '',
      contentDetailKo: object.contentDetailKo || '',
      uuid: object.uuid || null, // 保存UUID标识符
      isSaved: object.isSaved || false // 保存状态标记
    }
  }

  /**
   * 检查是否是新建的元素
   */
  isNewElement(object) {
    // 判断：isSaved为false的是新建元素
    return !object.isSaved
  }

  /**
   * 根据位置数据更新3D场景中的对象
   */
  updateSceneObjectFromPositionData(selectedPositionData, selectedObject, property, value) {
    if (!selectedPositionData || !selectedObject) return false

    // 直接使用当前选中的3D对象
    const sceneObject = selectedObject

    if (sceneObject) {
      // 更新名称
      if (property === 'name') {
        sceneObject.name = value
      }

      // 更新位置坐标
      if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
        const newPosition = {
          x: parseFloat(selectedPositionData.xcoordinate) || 0,
          y: parseFloat(selectedPositionData.ycoordinate) || 0,
          z: parseFloat(selectedPositionData.zcoordinate) || 0
        }

        sceneObject.position.copy(newPosition)
        if (sceneObject.mesh) {
          sceneObject.mesh.position.copy(newPosition)
        }
      }

      // 更新旋转
      if (property === 'yaw') {
        const newRotation = (parseFloat(selectedPositionData.yaw) || 0) * Math.PI / 180
        console.log(`更新位置 ${sceneObject.name} 的yaw: ${selectedPositionData.yaw}度 -> ${newRotation}弧度`)
        if (sceneObject.setRotation) {
          sceneObject.setRotation(newRotation)
        } else {
          console.warn(`位置对象 ${sceneObject.name} 没有setRotation方法`)
        }
      }

      // 更新业务属性
      if (property === 'actionNo') {
        sceneObject.actionNo = value || ''
      }

      // 更新多语言内容详情
      if (property === 'contentDetailZh') {
        sceneObject.contentDetailZh = value || ''
      }
      if (property === 'contentDetailEn') {
        sceneObject.contentDetailEn = value || ''
      }
      if (property === 'contentDetailJa') {
        sceneObject.contentDetailJa = value || ''
      }
      if (property === 'contentDetailKo') {
        sceneObject.contentDetailKo = value || ''
      }

      console.log(`已更新3D对象属性: ${property} = ${value}`)
      return true
    }
    return false
  }

  /**
   * 计算射线与地面的交点（Z轴向上）
   */
  calculateGroundIntersection(event, threeContainer, camera, pgmRenderer = null) {
    const rect = threeContainer.getBoundingClientRect()
    const mouse = new THREE.Vector2()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    const raycaster = new THREE.Raycaster()
    raycaster.setFromCamera(mouse, camera)

    // 与地面相交（Z=0平面作为地面）
    const plane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0)
    const intersectPoint = new THREE.Vector3()

    // 检查射线与平面的交点
    const intersection = raycaster.ray.intersectPlane(plane, intersectPoint)

    // 如果没有交点，使用默认位置
    if (!intersection) {
      console.warn('No intersection with ground plane, using default position')
      intersectPoint.set(0, 0, 0)
    }

    // 验证intersectPoint是有效的
    if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {
      console.warn('Invalid intersect point, using default position')
      intersectPoint.set(0, 0, 0)
    }

    // 如果有PGM渲染器且已渲染PGM，限制点在PGM范围内（Y轴水平）
    if (pgmRenderer && pgmRenderer.hasPGM()) {
      const clampedPoint = pgmRenderer.clampPointToPGMBounds(intersectPoint.x, intersectPoint.y)
      intersectPoint.x = clampedPoint.x
      intersectPoint.y = clampedPoint.y
      console.log('Position clamped to PGM bounds:', clampedPoint)
    }

    return intersectPoint
  }

  /**
   * 处理旋转操作
   */
  handleRotation(selectedPositionData, rotationDelta) {
    if (!selectedPositionData) return null

    const currentYaw = parseFloat(selectedPositionData.yaw) || 0
    let newYaw = currentYaw + rotationDelta

    // 保持角度在0-360范围内
    if (newYaw < 0) newYaw += 360
    if (newYaw >= 360) newYaw -= 360

    // 保留两位小数，避免浮点数精度问题
    return Math.round(newYaw * 100) / 100
  }



  /**
   * 验证并限制位置在PGM范围内（Y轴水平）
   */
  validateAndClampPosition(position, pgmRenderer) {
    if (!pgmRenderer || !pgmRenderer.hasPGM()) {
      return position // 如果没有PGM，返回原位置
    }

    const clampedPoint = pgmRenderer.clampPointToPGMBounds(position.x, position.y)
    return {
      x: clampedPoint.x,
      y: clampedPoint.y, // Y轴水平
      z: position.z      // Z轴保持不变（高度）
    }
  }

  /**
   * 检查位置是否在PGM范围内（Y轴水平）
   */
  isPositionInPGMBounds(position, pgmRenderer) {
    if (!pgmRenderer || !pgmRenderer.hasPGM()) {
      return true // 如果没有PGM，认为所有位置都有效
    }

    return pgmRenderer.isPointInPGMBounds(position.x, position.y)
  }
}
