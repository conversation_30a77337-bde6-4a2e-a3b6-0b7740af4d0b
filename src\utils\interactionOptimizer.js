/**
 * 交互优化工具
 * 专门用于优化大地图的交互性能
 */

export class InteractionOptimizer {
  constructor() {
    this.isEnabled = true
    this.optimizationLevel = 'auto'
    this.mapComplexityThresholds = {
      low: 100000,      // 10万像素以下
      medium: 500000,   // 50万像素以下
      high: 1000000,    // 100万像素以下
      ultra: 2000000    // 200万像素以下
    }

    this.currentOptimization = null
  }

  /**
   * 根据地图复杂度自动优化交互性能
   */
  optimizeForMap(mapData, dragManager, raycaster = null) {
    if (!this.isEnabled || !mapData) return

    const complexity = mapData.width * mapData.height
    const resolution = mapData.resolution || 0.05

    console.log(`地图复杂度分析: ${complexity}像素, 分辨率: ${resolution}m/pixel`)

    // 确定优化级别
    let level = 'ultra'
    if (complexity < this.mapComplexityThresholds.low) {
      level = 'low'
    } else if (complexity < this.mapComplexityThresholds.medium) {
      level = 'medium'
    } else if (complexity < this.mapComplexityThresholds.high) {
      level = 'high'
    }

    // 高分辨率地图需要额外优化
    if (resolution < 0.03) {
      level = 'ultra'
      console.log('检测到高分辨率地图，应用最高级别优化')
    }

    this.applyOptimization(level, dragManager, raycaster)
    this.currentOptimization = level

    return level
  }

  /**
   * 应用优化设置
   */
  applyOptimization(level, dragManager, raycaster) {
    console.log(`应用交互优化级别: ${level}`)

    const settings = this.getOptimizationSettings(level)

    // 优化拖拽管理器
    if (dragManager) {
      if (level === 'ultra' || level === 'high') {
        dragManager.enablePerformanceMode()
      } else {
        dragManager.disablePerformanceMode()
      }

      // 设置更新频率
      dragManager.updateThrottle = settings.updateThrottle
    }

    // 优化射线检测器
    if (raycaster) {
      raycaster.params.Points.threshold = settings.raycastThreshold
      raycaster.params.Line.threshold = settings.raycastThreshold
      raycaster.far = settings.raycastFar
    }

    console.log(`交互优化已应用: ${level}`, settings)
  }

  /**
   * 获取优化设置
   */
  getOptimizationSettings(level) {
    const settings = {
      low: {
        updateThrottle: 16,      // 60fps
        raycastThreshold: 0.05,
        raycastFar: 1000
      },
      medium: {
        updateThrottle: 20,      // 50fps
        raycastThreshold: 0.1,
        raycastFar: 800
      },
      high: {
        updateThrottle: 32,      // 30fps
        raycastThreshold: 0.15,
        raycastFar: 600
      },
      ultra: {
        updateThrottle: 50,      // 20fps
        raycastThreshold: 0.2,
        raycastFar: 400
      }
    }

    return settings[level] || settings.medium
  }

  /**
   * 获取优化建议
   */
  getOptimizationAdvice(mapData) {
    if (!mapData) return null

    const complexity = mapData.width * mapData.height
    const resolution = mapData.resolution || 0.05

    const advice = {
      level: this.currentOptimization,
      complexity: complexity,
      resolution: resolution,
      suggestions: []
    }

    if (complexity > this.mapComplexityThresholds.ultra) {
      advice.suggestions.push('地图像素数量极大，建议使用性能模式')
    }

    if (resolution < 0.025) {
      advice.suggestions.push('地图分辨率很高，建议降低交互更新频率')
    }

    if (complexity > this.mapComplexityThresholds.high && resolution < 0.03) {
      advice.suggestions.push('大尺寸高分辨率地图，建议启用所有性能优化')
    }

    return advice
  }

  /**
   * 重置优化设置
   */
  reset(dragManager, raycaster) {
    this.currentOptimization = null
    this.applyOptimization('medium', dragManager, raycaster)
    console.log('交互优化已重置为默认设置')
  }

  /**
   * 启用/禁用优化器
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
    console.log(`交互优化器${enabled ? '已启用' : '已禁用'}`)
  }
}

// 创建全局交互优化器实例
export const interactionOptimizer = new InteractionOptimizer()
